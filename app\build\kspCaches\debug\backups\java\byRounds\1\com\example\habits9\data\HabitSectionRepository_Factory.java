package com.example.habits9.data;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitSectionRepository_Factory implements Factory<HabitSectionRepository> {
  private final Provider<HabitSectionDao> habitSectionDaoProvider;

  public HabitSectionRepository_Factory(Provider<HabitSectionDao> habitSectionDaoProvider) {
    this.habitSectionDaoProvider = habitSectionDaoProvider;
  }

  @Override
  public HabitSectionRepository get() {
    return newInstance(habitSectionDaoProvider.get());
  }

  public static HabitSectionRepository_Factory create(
      Provider<HabitSectionDao> habitSectionDaoProvider) {
    return new HabitSectionRepository_Factory(habitSectionDaoProvider);
  }

  public static HabitSectionRepository newInstance(HabitSectionDao habitSectionDao) {
    return new HabitSectionRepository(habitSectionDao);
  }
}
