package com.example.habits9.di;

import com.example.habits9.data.CompletionDao;
import com.example.habits9.data.CompletionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideCompletionRepositoryFactory implements Factory<CompletionRepository> {
  private final Provider<CompletionDao> completionDaoProvider;

  public DatabaseModule_ProvideCompletionRepositoryFactory(
      Provider<CompletionDao> completionDaoProvider) {
    this.completionDaoProvider = completionDaoProvider;
  }

  @Override
  public CompletionRepository get() {
    return provideCompletionRepository(completionDaoProvider.get());
  }

  public static DatabaseModule_ProvideCompletionRepositoryFactory create(
      Provider<CompletionDao> completionDaoProvider) {
    return new DatabaseModule_ProvideCompletionRepositoryFactory(completionDaoProvider);
  }

  public static CompletionRepository provideCompletionRepository(CompletionDao completionDao) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideCompletionRepository(completionDao));
  }
}
