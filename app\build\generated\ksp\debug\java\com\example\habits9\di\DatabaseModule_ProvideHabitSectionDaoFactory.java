package com.example.habits9.di;

import com.example.habits9.data.HabitDatabase;
import com.example.habits9.data.HabitSectionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitSectionDaoFactory implements Factory<HabitSectionDao> {
  private final Provider<HabitDatabase> habitDatabaseProvider;

  public DatabaseModule_ProvideHabitSectionDaoFactory(
      Provider<HabitDatabase> habitDatabaseProvider) {
    this.habitDatabaseProvider = habitDatabaseProvider;
  }

  @Override
  public HabitSectionDao get() {
    return provideHabitSectionDao(habitDatabaseProvider.get());
  }

  public static DatabaseModule_ProvideHabitSectionDaoFactory create(
      Provider<HabitDatabase> habitDatabaseProvider) {
    return new DatabaseModule_ProvideHabitSectionDaoFactory(habitDatabaseProvider);
  }

  public static HabitSectionDao provideHabitSectionDao(HabitDatabase habitDatabase) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitSectionDao(habitDatabase));
  }
}
