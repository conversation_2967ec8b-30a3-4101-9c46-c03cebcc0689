{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-73:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e9549621c83de7ce5d44746d266914d0\\transformed\\credentials-1.2.0-rc01\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,113", "endOffsets": "160,274"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,215", "endColumns": "109,113", "endOffsets": "210,324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6806dc609573cce0b061254c5042724b\\transformed\\play-services-base-18.0.1\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1233,1341,1495,1619,1732,1874,1998,2114,2351,2502,2617,2773,2904,3048,3209,3282,3343", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "1336,1490,1614,1727,1869,1993,2109,2207,2497,2612,2768,2899,3043,3204,3277,3338,3418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4106571b134d49d860c08c6ca142a08e\\transformed\\play-services-basement-18.4.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2212", "endColumns": "138", "endOffsets": "2346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0062a5e1d214efb62fafece10c34c46\\transformed\\core-1.16.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "329,431,534,639,744,843,947,11116", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "426,529,634,739,838,942,1056,11212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\07f408d290f2498181cf432fde42d716\\transformed\\browser-1.4.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3423,3811,3918,4034", "endColumns": "107,106,115,104", "endOffsets": "3526,3913,4029,4134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\364994c224617bb5658cf91cfb556286\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,92", "endOffsets": "140,233"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11483,11573", "endColumns": "89,92", "endOffsets": "11568,11661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\92faffc5ee3d50988e6ae9072fb227ba\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,978,1060,1133,1208,1292,1373,1454,1521", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,973,1055,1128,1203,1287,1368,1449,1516,1634"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1061,1150,3531,3630,3729,4139,4224,10476,10562,10642,10721,10803,10876,10951,11035,11217,11298,11365", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "1145,1228,3625,3724,3806,4219,4310,10557,10637,10716,10798,10871,10946,11030,11111,11293,11360,11478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8aaeb18dd8abb7cc16bdd65ee58c7a94\\transformed\\material3-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4566,4652,4741,4835,4918,5001,5100,5200,5292,5393,5481,5592,5694,5806,5927,6009,6117", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4561,4647,4736,4830,4913,4996,5095,5195,5287,5388,5476,5587,5689,5801,5922,6004,6112,6211"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4315,4431,4548,4659,4776,4874,4971,5085,5214,5334,5473,5557,5663,5754,5851,5965,6093,6204,6332,6458,6590,6763,6887,7004,7124,7245,7337,7432,7551,7672,7773,7876,7980,8111,8247,8354,8451,8527,8623,8721,8826,8912,9001,9095,9178,9261,9360,9460,9552,9653,9741,9852,9954,10066,10187,10269,10377", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "4426,4543,4654,4771,4869,4966,5080,5209,5329,5468,5552,5658,5749,5846,5960,6088,6199,6327,6453,6585,6758,6882,6999,7119,7240,7332,7427,7546,7667,7768,7871,7975,8106,8242,8349,8446,8522,8618,8716,8821,8907,8996,9090,9173,9256,9355,9455,9547,9648,9736,9847,9949,10061,10182,10264,10372,10471"}}]}]}