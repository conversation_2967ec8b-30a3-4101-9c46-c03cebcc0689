package com.example.habits9.di;

import android.content.Context;
import com.example.habits9.data.HabitDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitDatabaseFactory implements Factory<HabitDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideHabitDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public HabitDatabase get() {
    return provideHabitDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideHabitDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideHabitDatabaseFactory(contextProvider);
  }

  public static HabitDatabase provideHabitDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitDatabase(context));
  }
}
