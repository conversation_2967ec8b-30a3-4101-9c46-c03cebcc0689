package com.example.habits9.di;

import com.example.habits9.data.HabitDao;
import com.example.habits9.data.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitRepositoryFactory implements Factory<HabitRepository> {
  private final Provider<HabitDao> habitDaoProvider;

  public DatabaseModule_ProvideHabitRepositoryFactory(Provider<HabitDao> habitDaoProvider) {
    this.habitDaoProvider = habitDaoProvider;
  }

  @Override
  public HabitRepository get() {
    return provideHabitRepository(habitDaoProvider.get());
  }

  public static DatabaseModule_ProvideHabitRepositoryFactory create(
      Provider<HabitDao> habitDaoProvider) {
    return new DatabaseModule_ProvideHabitRepositoryFactory(habitDaoProvider);
  }

  public static HabitRepository provideHabitRepository(HabitDao habitDao) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitRepository(habitDao));
  }
}
