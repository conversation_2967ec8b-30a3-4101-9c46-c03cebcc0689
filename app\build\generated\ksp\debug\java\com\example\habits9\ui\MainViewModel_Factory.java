package com.example.habits9.ui;

import com.example.habits9.data.CompletionRepository;
import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<CompletionRepository> completionRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public MainViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.completionRepositoryProvider = completionRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(habitRepositoryProvider.get(), completionRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static MainViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new MainViewModel_Factory(habitRepositoryProvider, completionRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static MainViewModel newInstance(HabitRepository habitRepository,
      CompletionRepository completionRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new MainViewModel(habitRepository, completionRepository, userPreferencesRepository);
  }
}
