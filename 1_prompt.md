# 📝 Prompt: Implement User Authentication Screen

## 1. Objective

To create a simple, clean user interface that allows new users to sign up and existing users to sign in using their email and password. This feature will be powered by the Firebase Authentication SDK we've already integrated.

## 2. Detailed Implementation Plan

The implementation involves creating a new UI screen and a `ViewModel` to handle the authentication logic.

### Task 2.1: Build the Authentication UI Screen

* **Goal:** Create a new screen that serves as the entry point for the app for any logged-out user.
* **Action:**
    * Create a new screen (e.g., `AuthScreen.kt`).
    * The UI must be clean and minimal, adhering to the `style.md` guide for colors, typography, and spacing.
    * The screen must contain the following components:
        * An "Email" text input field.
        * A "Password" text input field (the input should be masked).
        * A "Sign In" button.
        * A "Sign Up" button.
        * A text label to display any error messages from Firebase (e.g., "Invalid email format," "Wrong password").

### Task 2.2: Implement the Authentication ViewModel

* **Goal:** Create a `ViewModel` to manage the state and business logic for signing in and signing up users.
* **Action:**
    * Create a new `AuthViewModel.kt`.
    * This `ViewModel` must hold the state for the email and password input fields.
    * Implement a **`signUp()`** function:
        * This function will be called when the "Sign Up" button is tapped.
        * It should call the Firebase SDK's `createUserWithEmailAndPassword()` method, passing the email and password from the UI.
        * It must handle both success and failure cases. On success, it should navigate the user to the main app screen. On failure, it should update the error message state to be displayed on the UI.
    * Implement a **`signIn()`** function:
        * This function will be called when the "Sign In" button is tapped.
        * It should call the Firebase SDK's `signInWithEmailAndPassword()` method.
        * It must also handle both success (navigate to the main screen) and failure (display an error message).

### Task 2.3: Implement App Startup Logic

* **Goal:** The app needs to know whether to show the Authentication screen or the main Home screen on startup.
* **Action:**
    * In your `MainActivity.kt` or main navigation graph, add logic to check if a user is currently signed in.
    * Use `Firebase.auth.currentUser` to check the current user's state.
    * If `currentUser` is **not null**, navigate directly to the `HomeScreen`.
    * If `currentUser` is **null**, navigate to the `AuthScreen`.

## 3. Verification / Testing Section

To confirm the authentication flow is working correctly, perform the following tests:

* **Test Case 1: Sign Up a New User:**
    * Open the app and verify you are on the `AuthScreen`.
    * Enter a valid new email address and a password (at least 6 characters).
    * Click the "Sign Up" button.
    * **Expected Outcome:** A new user should be created in the Firebase Authentication console, and you should be automatically navigated to the app's main home screen.

* **Test Case 2: Sign Up with Invalid Data:**
    * Attempt to sign up with an invalid email (e.g., "test@test").
    * **Expected Outcome:** The app should not crash. An appropriate error message (e.g., "The email address is badly formatted.") should appear on the screen.

* **Test Case 3: Sign In and Sign Out:**
    * Close and reopen the app. You should land directly on the `HomeScreen` because you are still signed in.
    * (You may need to temporarily add a "Sign Out" button in a settings menu for this test). Sign out of the app.
    * You should be returned to the `AuthScreen`.
    * Enter the credentials you created in Test Case 1 and click "Sign In".
    * **Expected Outcome:** You should be successfully logged in and navigated to the `HomeScreen`.

## 4. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
- Reference project doesn't use firebase. So if needed we can skip this step

Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.