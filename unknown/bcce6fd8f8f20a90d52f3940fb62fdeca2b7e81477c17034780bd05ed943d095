package com.example.habits9.di;

import com.example.habits9.data.CompletionDao;
import com.example.habits9.data.HabitDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideCompletionDaoFactory implements Factory<CompletionDao> {
  private final Provider<HabitDatabase> habitDatabaseProvider;

  public DatabaseModule_ProvideCompletionDaoFactory(Provider<HabitDatabase> habitDatabaseProvider) {
    this.habitDatabaseProvider = habitDatabaseProvider;
  }

  @Override
  public CompletionDao get() {
    return provideCompletionDao(habitDatabaseProvider.get());
  }

  public static DatabaseModule_ProvideCompletionDaoFactory create(
      Provider<HabitDatabase> habitDatabaseProvider) {
    return new DatabaseModule_ProvideCompletionDaoFactory(habitDatabaseProvider);
  }

  public static CompletionDao provideCompletionDao(HabitDatabase habitDatabase) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideCompletionDao(habitDatabase));
  }
}
