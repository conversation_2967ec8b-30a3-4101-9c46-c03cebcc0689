package com.example.habits9.data;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CompletionRepository_Factory implements Factory<CompletionRepository> {
  private final Provider<CompletionDao> completionDaoProvider;

  public CompletionRepository_Factory(Provider<CompletionDao> completionDaoProvider) {
    this.completionDaoProvider = completionDaoProvider;
  }

  @Override
  public CompletionRepository get() {
    return newInstance(completionDaoProvider.get());
  }

  public static CompletionRepository_Factory create(Provider<CompletionDao> completionDaoProvider) {
    return new CompletionRepository_Factory(completionDaoProvider);
  }

  public static CompletionRepository newInstance(CompletionDao completionDao) {
    return new CompletionRepository(completionDao);
  }
}
