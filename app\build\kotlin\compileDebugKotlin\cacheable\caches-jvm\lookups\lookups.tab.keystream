  Activity android.app  Application android.app  Build android.app.Activity  CreateMeasurableHabitScreen android.app.Activity  CreateYesNoHabitScreen android.app.Activity  HabitDetailsScreen android.app.Activity  HabitTypeSelectionScreen android.app.Activity  
HomeScreen android.app.Activity  ManageSectionsScreen android.app.Activity  NavHost android.app.Activity  SettingsScreen android.app.Activity  UHabits_99Theme android.app.Activity  
composable android.app.Activity  onCreate android.app.Activity  rememberNavController android.app.Activity  
setContent android.app.Activity  Context android.content  Build android.content.Context  CreateMeasurableHabitScreen android.content.Context  CreateYesNoHabitScreen android.content.Context  HabitDetailsScreen android.content.Context  HabitTypeSelectionScreen android.content.Context  
HomeScreen android.content.Context  ManageSectionsScreen android.content.Context  NavHost android.content.Context  SettingsScreen android.content.Context  UHabits_99Theme android.content.Context  
composable android.content.Context  	dataStore android.content.Context  rememberNavController android.content.Context  
setContent android.content.Context  Build android.content.ContextWrapper  CreateMeasurableHabitScreen android.content.ContextWrapper  CreateYesNoHabitScreen android.content.ContextWrapper  HabitDetailsScreen android.content.ContextWrapper  HabitTypeSelectionScreen android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  ManageSectionsScreen android.content.ContextWrapper  NavHost android.content.ContextWrapper  SettingsScreen android.content.ContextWrapper  UHabits_99Theme android.content.ContextWrapper  
composable android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  d android.util.Log  Build  android.view.ContextThemeWrapper  CreateMeasurableHabitScreen  android.view.ContextThemeWrapper  CreateYesNoHabitScreen  android.view.ContextThemeWrapper  HabitDetailsScreen  android.view.ContextThemeWrapper  HabitTypeSelectionScreen  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  ManageSectionsScreen  android.view.ContextThemeWrapper  NavHost  android.view.ContextThemeWrapper  SettingsScreen  android.view.ContextThemeWrapper  UHabits_99Theme  android.view.ContextThemeWrapper  
composable  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CreateMeasurableHabitScreen #androidx.activity.ComponentActivity  CreateYesNoHabitScreen #androidx.activity.ComponentActivity  HabitDetailsScreen #androidx.activity.ComponentActivity  HabitTypeSelectionScreen #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  ManageSectionsScreen #androidx.activity.ComponentActivity  NavHost #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  SettingsScreen #androidx.activity.ComponentActivity  UHabits_99Theme #androidx.activity.ComponentActivity  
composable #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  CreateMeasurableHabitScreen -androidx.activity.ComponentActivity.Companion  CreateYesNoHabitScreen -androidx.activity.ComponentActivity.Companion  HabitDetailsScreen -androidx.activity.ComponentActivity.Companion  HabitTypeSelectionScreen -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  ManageSectionsScreen -androidx.activity.ComponentActivity.Companion  NavHost -androidx.activity.ComponentActivity.Companion  SettingsScreen -androidx.activity.ComponentActivity.Companion  UHabits_99Theme -androidx.activity.ComponentActivity.Companion  
composable -androidx.activity.ComponentActivity.Companion  rememberNavController -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  RequiresApi androidx.annotation  AnimatedContentScope androidx.compose.animation  animateContentSize androidx.compose.animation  CreateMeasurableHabitScreen /androidx.compose.animation.AnimatedContentScope  CreateYesNoHabitScreen /androidx.compose.animation.AnimatedContentScope  HabitDetailsScreen /androidx.compose.animation.AnimatedContentScope  HabitTypeSelectionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  ManageSectionsScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  animateDpAsState androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  horizontalScroll androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation   detectDragGesturesAfterLongPress $androidx.compose.foundation.gestures  
AccentPrimary "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BackgroundDark "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  BorderStroke "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CreateHabitViewModel "androidx.compose.foundation.layout  DarkBackground "androidx.compose.foundation.layout  DayChip "androidx.compose.foundation.layout  	DayOfWeek "androidx.compose.foundation.layout  DividerColor "androidx.compose.foundation.layout  Double "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  EnhancedFrequency "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
FrequencyType "androidx.compose.foundation.layout  FrequencyTypeTab "androidx.compose.foundation.layout  GhostPlaceholder "androidx.compose.foundation.layout  HabitSection "androidx.compose.foundation.layout  HapticFeedbackType "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  	LocalTime "androidx.compose.foundation.layout  ManageSectionsViewModel "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MonthlyOptions "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  
ReminderState "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  
SectionColors "androidx.compose.foundation.layout  SectionListItem "androidx.compose.foundation.layout  Set "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SurfaceVariantDark "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  SwitchDefaults "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextPrimary "androidx.compose.foundation.layout  
TextSecondary "androidx.compose.foundation.layout  
TimePicker "androidx.compose.foundation.layout  TimePickerDefaults "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  
WeeklyOptions "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  alpha "androidx.compose.foundation.layout  animateContentSize "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  coerceAtMost "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  focusRequester "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  indexOfFirst "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  	lowercase "androidx.compose.foundation.layout  minus "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  pointerInput "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberTimePickerState "androidx.compose.foundation.layout  replaceFirstChar "androidx.compose.foundation.layout  setOf "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toDoubleOrNull "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  toSet "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  zIndex "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccentPrimary +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BackgroundDark +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  CompletionIndicator +androidx.compose.foundation.layout.BoxScope  DarkBackground +androidx.compose.foundation.layout.BoxScope  DividerColor +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  GhostPlaceholder +androidx.compose.foundation.layout.BoxScope  HapticFeedbackType +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  SectionListItem +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  SurfaceVariantDark +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TextPrimary +androidx.compose.foundation.layout.BoxScope  
TextSecondary +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  animateContentSize +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  coerceAtMost +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
drawBehind +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  find +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  horizontalScroll +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  
isNullOrEmpty +androidx.compose.foundation.layout.BoxScope  isWeekStartDay +androidx.compose.foundation.layout.BoxScope  itemsIndexed +androidx.compose.foundation.layout.BoxScope  	lowercase +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  
plusAssign +androidx.compose.foundation.layout.BoxScope  replaceFirstChar +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  take +androidx.compose.foundation.layout.BoxScope  toDoubleOrNull +androidx.compose.foundation.layout.BoxScope  	uppercase +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  
AccentPrimary .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BackgroundDark .androidx.compose.foundation.layout.ColumnScope  BorderStroke .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DarkBackground .androidx.compose.foundation.layout.ColumnScope  DayChip .androidx.compose.foundation.layout.ColumnScope  	DayOfWeek .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  DividerColor .androidx.compose.foundation.layout.ColumnScope  
DragIndicator .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
FrequencyType .androidx.compose.foundation.layout.ColumnScope  FrequencyTypeTab .androidx.compose.foundation.layout.ColumnScope  FrozenPaneLayout .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  
MetricView .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MonthlyOptions .androidx.compose.foundation.layout.ColumnScope  Offset .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  OverviewSection .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RadioButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SectionColors .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  SubHeaderRow .androidx.compose.foundation.layout.ColumnScope  SurfaceVariantDark .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  SwitchDefaults .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TextPrimary .androidx.compose.foundation.layout.ColumnScope  
TextSecondary .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  
WeeklyOptions .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope   detectDragGesturesAfterLongPress .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  
drawBehind .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  focusRequester .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  horizontalScroll .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  isWeekStartDay .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  minus .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  pointerInput .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  selectableGroup .androidx.compose.foundation.layout.ColumnScope  setOf .androidx.compose.foundation.layout.ColumnScope  shadow .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  toDoubleOrNull .androidx.compose.foundation.layout.ColumnScope  toSet .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  zIndex .androidx.compose.foundation.layout.ColumnScope  
AccentPrimary +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  BackgroundDark +androidx.compose.foundation.layout.RowScope  BorderStroke +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CompletionIndicator +androidx.compose.foundation.layout.RowScope  DarkBackground +androidx.compose.foundation.layout.RowScope  	DateRange +androidx.compose.foundation.layout.RowScope  	DayOfWeek +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  DividerColor +androidx.compose.foundation.layout.RowScope  
DragIndicator +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  
FrequencyType +androidx.compose.foundation.layout.RowScope  FrequencyTypeTab +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MetricView +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  NotificationsOff +androidx.compose.foundation.layout.RowScope  Offset +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RadioButtonDefaults +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  SurfaceVariantDark +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  SwitchDefaults +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextPrimary +androidx.compose.foundation.layout.RowScope  
TextSecondary +androidx.compose.foundation.layout.RowScope  alpha +androidx.compose.foundation.layout.RowScope  android +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  com +androidx.compose.foundation.layout.RowScope   detectDragGesturesAfterLongPress +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
drawBehind +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  horizontalScroll +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  
isNullOrEmpty +androidx.compose.foundation.layout.RowScope  isWeekStartDay +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  minus +androidx.compose.foundation.layout.RowScope  outlinedButtonColors +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  plus +androidx.compose.foundation.layout.RowScope  pointerInput +androidx.compose.foundation.layout.RowScope  setOf +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  toDoubleOrNull +androidx.compose.foundation.layout.RowScope  toSet +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  example &androidx.compose.foundation.layout.com  habits9 .androidx.compose.foundation.layout.com.example  data 6androidx.compose.foundation.layout.com.example.habits9  HabitSection ;androidx.compose.foundation.layout.com.example.habits9.data  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  LazyListItemInfo  androidx.compose.foundation.lazy  LazyListLayoutInfo  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  
AccentPrimary .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  CircleShape .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyItemScope  DayChip .androidx.compose.foundation.lazy.LazyItemScope  DividerColor .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  GhostPlaceholder .androidx.compose.foundation.lazy.LazyItemScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Offset .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  SectionListItem .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TextPrimary .androidx.compose.foundation.lazy.LazyItemScope  
TextSecondary .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyItemScope  animateContentSize .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  coerceAtMost .androidx.compose.foundation.lazy.LazyItemScope  com .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  
drawBehind .androidx.compose.foundation.lazy.LazyItemScope  find .androidx.compose.foundation.lazy.LazyItemScope  forEachIndexed .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  horizontalScroll .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  
isNullOrEmpty .androidx.compose.foundation.lazy.LazyItemScope  minus .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  plus .androidx.compose.foundation.lazy.LazyItemScope  
plusAssign .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  index 1androidx.compose.foundation.lazy.LazyListItemInfo  offset 1androidx.compose.foundation.lazy.LazyListItemInfo  size 1androidx.compose.foundation.lazy.LazyListItemInfo  visibleItemsInfo 3androidx.compose.foundation.lazy.LazyListLayoutInfo  
AccentPrimary .androidx.compose.foundation.lazy.LazyListScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  CircleShape .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  CompletionIndicator .androidx.compose.foundation.lazy.LazyListScope  DayChip .androidx.compose.foundation.lazy.LazyListScope  	DayOfWeek .androidx.compose.foundation.lazy.LazyListScope  DividerColor .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  GhostPlaceholder .androidx.compose.foundation.lazy.LazyListScope  HapticFeedbackType .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Offset .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  
SectionColors .androidx.compose.foundation.lazy.LazyListScope  SectionListItem .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  SurfaceVariantDark .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TextPrimary .androidx.compose.foundation.lazy.LazyListScope  
TextSecondary .androidx.compose.foundation.lazy.LazyListScope  android .androidx.compose.foundation.lazy.LazyListScope  animateContentSize .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  coerceAtMost .androidx.compose.foundation.lazy.LazyListScope  com .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  
drawBehind .androidx.compose.foundation.lazy.LazyListScope  find .androidx.compose.foundation.lazy.LazyListScope  forEachIndexed .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  horizontalScroll .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  
isNullOrEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  minus .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  plus .androidx.compose.foundation.lazy.LazyListScope  
plusAssign .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  
layoutInfo .androidx.compose.foundation.lazy.LazyListState  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  toDoubleOrNull 4androidx.compose.foundation.text.KeyboardActionScope  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	DateRange ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  
DragIndicator ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  NotificationsOff ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  
DragIndicator &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsOff &androidx.compose.material.icons.filled  
AccentPrimary androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  BackgroundDark androidx.compose.material3  Boolean androidx.compose.material3  BorderStroke androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  CreateHabitViewModel androidx.compose.material3  DarkBackground androidx.compose.material3  DayChip androidx.compose.material3  	DayOfWeek androidx.compose.material3  DividerColor androidx.compose.material3  Double androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  EnhancedFrequency androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FontWeight androidx.compose.material3  
FrequencyType androidx.compose.material3  FrequencyTypeTab androidx.compose.material3  GhostPlaceholder androidx.compose.material3  HabitSection androidx.compose.material3  HapticFeedbackType androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  	ImeAction androidx.compose.material3  Int androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  List androidx.compose.material3  	LocalTime androidx.compose.material3  ManageSectionsViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MonthlyOptions androidx.compose.material3  Offset androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
PaddingValues androidx.compose.material3  RadioButton androidx.compose.material3  RadioButtonColors androidx.compose.material3  RadioButtonDefaults androidx.compose.material3  
ReminderState androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  
SectionColors androidx.compose.material3  SectionListItem androidx.compose.material3  Set androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  SurfaceVariantDark androidx.compose.material3  Switch androidx.compose.material3  SwitchColors androidx.compose.material3  SwitchDefaults androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  TextPrimary androidx.compose.material3  
TextSecondary androidx.compose.material3  
TimePicker androidx.compose.material3  TimePickerColors androidx.compose.material3  TimePickerDefaults androidx.compose.material3  TimePickerState androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  
WeeklyOptions androidx.compose.material3  align androidx.compose.material3  alpha androidx.compose.material3  animateContentSize androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  coerceAtMost androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  focusRequester androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  indexOfFirst androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  	lowercase androidx.compose.material3  minus androidx.compose.material3  mutableStateOf androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  
plusAssign androidx.compose.material3  pointerInput androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  rememberTimePickerState androidx.compose.material3  replaceFirstChar androidx.compose.material3  setOf androidx.compose.material3  setValue androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  take androidx.compose.material3  takeIf androidx.compose.material3  to androidx.compose.material3  toDoubleOrNull androidx.compose.material3  toIntOrNull androidx.compose.material3  toList androidx.compose.material3  toSet androidx.compose.material3  topAppBarColors androidx.compose.material3  	uppercase androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  zIndex androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  
AccentPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DividerColor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextFieldDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  SurfaceVariantDark 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TextPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
TextSecondary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
background 6androidx.compose.material3.ExposedDropdownMenuBoxScope  colors 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors .androidx.compose.material3.RadioButtonDefaults  colors )androidx.compose.material3.SwitchDefaults  colors -androidx.compose.material3.TimePickerDefaults  hour *androidx.compose.material3.TimePickerState  minute *androidx.compose.material3.TimePickerState  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  example androidx.compose.material3.com  habits9 &androidx.compose.material3.com.example  data .androidx.compose.material3.com.example.habits9  HabitSection 3androidx.compose.material3.com.example.habits9.data  
AccentPrimary androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Anchor androidx.compose.runtime  Arrangement androidx.compose.runtime  BackgroundDark androidx.compose.runtime  Boolean androidx.compose.runtime  BorderStroke androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CreateHabitViewModel androidx.compose.runtime  DarkBackground androidx.compose.runtime  DayChip androidx.compose.runtime  	DayOfWeek androidx.compose.runtime  DividerColor androidx.compose.runtime  Double androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  EnhancedFrequency androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  
FontWeight androidx.compose.runtime  
FrequencyType androidx.compose.runtime  FrequencyTypeTab androidx.compose.runtime  GhostPlaceholder androidx.compose.runtime  HabitSection androidx.compose.runtime  HapticFeedbackType androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  	ImeAction androidx.compose.runtime  Int androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  	LocalTime androidx.compose.runtime  ManageSectionsViewModel androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MonthlyOptions androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
ReminderState androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  
SectionColors androidx.compose.runtime  SectionListItem androidx.compose.runtime  Set androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SurfaceVariantDark androidx.compose.runtime  Switch androidx.compose.runtime  SwitchDefaults androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TextPrimary androidx.compose.runtime  
TextSecondary androidx.compose.runtime  
TimePicker androidx.compose.runtime  TimePickerDefaults androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  
WeeklyOptions androidx.compose.runtime  align androidx.compose.runtime  alpha androidx.compose.runtime  animateContentSize androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  coerceAtMost androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  focusRequester androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  indexOfFirst androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  	lowercase androidx.compose.runtime  minus androidx.compose.runtime  mutableStateOf androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  
plusAssign androidx.compose.runtime  pointerInput androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberTimePickerState androidx.compose.runtime  replaceFirstChar androidx.compose.runtime  setOf androidx.compose.runtime  setValue androidx.compose.runtime  shadow androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  take androidx.compose.runtime  takeIf androidx.compose.runtime  to androidx.compose.runtime  toDoubleOrNull androidx.compose.runtime  toIntOrNull androidx.compose.runtime  toList androidx.compose.runtime  toSet androidx.compose.runtime  topAppBarColors androidx.compose.runtime  	uppercase androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  zIndex androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  example androidx.compose.runtime.com  habits9 $androidx.compose.runtime.com.example  data ,androidx.compose.runtime.com.example.habits9  HabitSection 1androidx.compose.runtime.com.example.habits9.data  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  zIndex androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  animateContentSize androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
drawBehind androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  focusRequester androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  horizontalScroll androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  selectableGroup androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  alpha &androidx.compose.ui.Modifier.Companion  animateContentSize &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  horizontalScroll &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  selectableGroup &androidx.compose.ui.Modifier.Companion  shadow &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  
drawBehind androidx.compose.ui.draw  scale androidx.compose.ui.draw  shadow androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  requestFocus (androidx.compose.ui.focus.FocusRequester  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  
plusAssign #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  height !androidx.compose.ui.geometry.Size  minDimension !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  scaleX /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  translationY /androidx.compose.ui.graphics.GraphicsLayerScope  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  
AccentPrimary 0androidx.compose.ui.graphics.drawscope.DrawScope  DividerColor 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  
TextSecondary 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  TextHandleMove 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  TextHandleMove ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  consume 4androidx.compose.ui.input.pointer.PointerInputChange   detectDragGesturesAfterLongPress 3androidx.compose.ui.input.pointer.PointerInputScope  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  Role androidx.compose.ui.semantics  	Companion "androidx.compose.ui.semantics.Role  RadioButton "androidx.compose.ui.semantics.Role  RadioButton ,androidx.compose.ui.semantics.Role.Companion  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Decimal +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  plus androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CreateMeasurableHabitScreen #androidx.core.app.ComponentActivity  CreateYesNoHabitScreen #androidx.core.app.ComponentActivity  HabitDetailsScreen #androidx.core.app.ComponentActivity  HabitTypeSelectionScreen #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  ManageSectionsScreen #androidx.core.app.ComponentActivity  NavHost #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  SettingsScreen #androidx.core.app.ComponentActivity  UHabits_99Theme #androidx.core.app.ComponentActivity  
composable #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  CreateMeasurableHabitScreen #androidx.navigation.NavGraphBuilder  CreateYesNoHabitScreen #androidx.navigation.NavGraphBuilder  HabitDetailsScreen #androidx.navigation.NavGraphBuilder  HabitTypeSelectionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  ManageSectionsScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
Completion 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  
ForeignKey 
androidx.room  Habit 
androidx.room  HabitSection 
androidx.room  Index 
androidx.room  Insert 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  	Companion androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  
CompletionDao androidx.room.RoomDatabase  HabitDao androidx.room.RoomDatabase  HabitSectionDao androidx.room.RoomDatabase  
addMigrations "androidx.room.RoomDatabase.Builder  build "androidx.room.RoomDatabase.Builder  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  Application com.example.habits9  HabitsApplication com.example.habits9  HiltAndroidApp com.example.habits9  AT_LEAST com.example.habits9.data  AT_MOST com.example.habits9.data  
AccentPrimary com.example.habits9.data  AlertDialog com.example.habits9.data  	Alignment com.example.habits9.data  ApplicationContext com.example.habits9.data  Arrangement com.example.habits9.data  BackgroundDark com.example.habits9.data  Boolean com.example.habits9.data  Box com.example.habits9.data  Column com.example.habits9.data  
Completion com.example.habits9.data  
CompletionDao com.example.habits9.data  CompletionRepository com.example.habits9.data  
Composable com.example.habits9.data  Context com.example.habits9.data  DAILY com.example.habits9.data  Dao com.example.habits9.data  	DataStore com.example.habits9.data  Database com.example.habits9.data  DatabaseFrequency com.example.habits9.data  DayChip com.example.habits9.data  	DayOfWeek com.example.habits9.data  Delete com.example.habits9.data  DividerColor com.example.habits9.data  Double com.example.habits9.data  EnhancedFrequency com.example.habits9.data  Entity com.example.habits9.data  Flow com.example.habits9.data  
FontWeight com.example.habits9.data  
ForeignKey com.example.habits9.data  
FrequencyType com.example.habits9.data  FrequencyTypeTab com.example.habits9.data  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitFrequency com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  	HabitType com.example.habits9.data  IllegalStateException com.example.habits9.data  Inject com.example.habits9.data  Insert com.example.habits9.data  Int com.example.habits9.data  LazyRow com.example.habits9.data  List com.example.habits9.data  Long com.example.habits9.data  MONTHLY com.example.habits9.data  
MaterialTheme com.example.habits9.data  Modifier com.example.habits9.data  MonthlyOptions com.example.habits9.data  	NUMERICAL com.example.habits9.data  NumericalHabitType com.example.habits9.data  OnConflictStrategy com.example.habits9.data  OutlinedTextField com.example.habits9.data  OutlinedTextFieldDefaults com.example.habits9.data  Preferences com.example.habits9.data  PreferencesKeys com.example.habits9.data  
PrimaryKey com.example.habits9.data  Query com.example.habits9.data  RoomDatabase com.example.habits9.data  Row com.example.habits9.data  Set com.example.habits9.data  	Singleton com.example.habits9.data  Spacer com.example.habits9.data  String com.example.habits9.data  System com.example.habits9.data  Text com.example.habits9.data  
TextButton com.example.habits9.data  TextPrimary com.example.habits9.data  
TextSecondary com.example.habits9.data  UUID com.example.habits9.data  Unit com.example.habits9.data  Update com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  WEEKLY com.example.habits9.data  
WeeklyOptions com.example.habits9.data  YES_NO com.example.habits9.data  all com.example.habits9.data  any com.example.habits9.data  colors com.example.habits9.data  contains com.example.habits9.data  	dataStore com.example.habits9.data  edit com.example.habits9.data  	emptyList com.example.habits9.data  fillMaxWidth com.example.habits9.data  find com.example.habits9.data  first com.example.habits9.data  forEach com.example.habits9.data  fromInt com.example.habits9.data  
fromString com.example.habits9.data  	fromValue com.example.habits9.data  getValue com.example.habits9.data  height com.example.habits9.data  
isNotEmpty com.example.habits9.data  
isNullOrBlank com.example.habits9.data  joinToString com.example.habits9.data  let com.example.habits9.data  listOf com.example.habits9.data  	lowercase com.example.habits9.data  map com.example.habits9.data  
mapNotNull com.example.habits9.data  minus com.example.habits9.data  mutableStateOf com.example.habits9.data  padding com.example.habits9.data  plus com.example.habits9.data  provideDelegate com.example.habits9.data  remember com.example.habits9.data  replace com.example.habits9.data  replaceFirstChar com.example.habits9.data  setOf com.example.habits9.data  setValue com.example.habits9.data  size com.example.habits9.data  spacedBy com.example.habits9.data  split com.example.habits9.data  stringPreferencesKey com.example.habits9.data  take com.example.habits9.data  toEnhancedFrequency com.example.habits9.data  toIntOrNull com.example.habits9.data  toLegacyFrequency com.example.habits9.data  toList com.example.habits9.data  toSet com.example.habits9.data  toString com.example.habits9.data  	uppercase com.example.habits9.data  values com.example.habits9.data  weight com.example.habits9.data  width com.example.habits9.data  copy #com.example.habits9.data.Completion  habitId #com.example.habits9.data.Completion  	timestamp #com.example.habits9.data.Completion  value #com.example.habits9.data.Completion  OnConflictStrategy &com.example.habits9.data.CompletionDao  deleteAllCompletionsForHabit &com.example.habits9.data.CompletionDao  deleteCompletion &com.example.habits9.data.CompletionDao  getCompletionForHabitAndDate &com.example.habits9.data.CompletionDao  getCompletionsForHabit &com.example.habits9.data.CompletionDao  getCompletionsForHabitsInRange &com.example.habits9.data.CompletionDao  insertCompletion &com.example.habits9.data.CompletionDao  updateCompletion &com.example.habits9.data.CompletionDao  
completionDao -com.example.habits9.data.CompletionRepository  deleteCompletion -com.example.habits9.data.CompletionRepository  getCompletionForHabitAndDate -com.example.habits9.data.CompletionRepository  getCompletionsForHabitsInRange -com.example.habits9.data.CompletionRepository  insertCompletion -com.example.habits9.data.CompletionRepository  updateCompletion -com.example.habits9.data.CompletionRepository  
dayOfMonth *com.example.habits9.data.DatabaseFrequency  dayOfWeekInMonth *com.example.habits9.data.DatabaseFrequency  
daysOfWeek *com.example.habits9.data.DatabaseFrequency  
frequencyType *com.example.habits9.data.DatabaseFrequency  repeatsEvery *com.example.habits9.data.DatabaseFrequency  weekOfMonth *com.example.habits9.data.DatabaseFrequency  	Companion "com.example.habits9.data.DayOfWeek  	DayOfWeek "com.example.habits9.data.DayOfWeek  FRIDAY "com.example.habits9.data.DayOfWeek  Int "com.example.habits9.data.DayOfWeek  List "com.example.habits9.data.DayOfWeek  MONDAY "com.example.habits9.data.DayOfWeek  SATURDAY "com.example.habits9.data.DayOfWeek  SUNDAY "com.example.habits9.data.DayOfWeek  String "com.example.habits9.data.DayOfWeek  THURSDAY "com.example.habits9.data.DayOfWeek  TUESDAY "com.example.habits9.data.DayOfWeek  	WEDNESDAY "com.example.habits9.data.DayOfWeek  	emptyList "com.example.habits9.data.DayOfWeek  find "com.example.habits9.data.DayOfWeek  
fromString "com.example.habits9.data.DayOfWeek  	fromValue "com.example.habits9.data.DayOfWeek  fullName "com.example.habits9.data.DayOfWeek  
isNullOrBlank "com.example.habits9.data.DayOfWeek  joinToString "com.example.habits9.data.DayOfWeek  map "com.example.habits9.data.DayOfWeek  
mapNotNull "com.example.habits9.data.DayOfWeek  	shortName "com.example.habits9.data.DayOfWeek  split "com.example.habits9.data.DayOfWeek  toIntOrNull "com.example.habits9.data.DayOfWeek  toString "com.example.habits9.data.DayOfWeek  value "com.example.habits9.data.DayOfWeek  values "com.example.habits9.data.DayOfWeek  	emptyList ,com.example.habits9.data.DayOfWeek.Companion  find ,com.example.habits9.data.DayOfWeek.Companion  
fromString ,com.example.habits9.data.DayOfWeek.Companion  	fromValue ,com.example.habits9.data.DayOfWeek.Companion  
isNullOrBlank ,com.example.habits9.data.DayOfWeek.Companion  joinToString ,com.example.habits9.data.DayOfWeek.Companion  map ,com.example.habits9.data.DayOfWeek.Companion  
mapNotNull ,com.example.habits9.data.DayOfWeek.Companion  split ,com.example.habits9.data.DayOfWeek.Companion  toIntOrNull ,com.example.habits9.data.DayOfWeek.Companion  toString ,com.example.habits9.data.DayOfWeek.Companion  values ,com.example.habits9.data.DayOfWeek.Companion  Boolean *com.example.habits9.data.EnhancedFrequency  	Companion *com.example.habits9.data.EnhancedFrequency  DAILY *com.example.habits9.data.EnhancedFrequency  DatabaseFrequency *com.example.habits9.data.EnhancedFrequency  	DayOfWeek *com.example.habits9.data.EnhancedFrequency  EnhancedFrequency *com.example.habits9.data.EnhancedFrequency  
FrequencyType *com.example.habits9.data.EnhancedFrequency  HabitFrequency *com.example.habits9.data.EnhancedFrequency  Int *com.example.habits9.data.EnhancedFrequency  List *com.example.habits9.data.EnhancedFrequency  String *com.example.habits9.data.EnhancedFrequency  all *com.example.habits9.data.EnhancedFrequency  any *com.example.habits9.data.EnhancedFrequency  contains *com.example.habits9.data.EnhancedFrequency  
dayOfMonth *com.example.habits9.data.EnhancedFrequency  dayOfWeekInMonth *com.example.habits9.data.EnhancedFrequency  
daysOfWeek *com.example.habits9.data.EnhancedFrequency  	emptyList *com.example.habits9.data.EnhancedFrequency  first *com.example.habits9.data.EnhancedFrequency  fromDatabaseValues *com.example.habits9.data.EnhancedFrequency  
fromString *com.example.habits9.data.EnhancedFrequency  	fromValue *com.example.habits9.data.EnhancedFrequency  getOrdinalSuffix *com.example.habits9.data.EnhancedFrequency  getValidationError *com.example.habits9.data.EnhancedFrequency  
isNotEmpty *com.example.habits9.data.EnhancedFrequency  isValid *com.example.habits9.data.EnhancedFrequency  joinToString *com.example.habits9.data.EnhancedFrequency  let *com.example.habits9.data.EnhancedFrequency  listOf *com.example.habits9.data.EnhancedFrequency  repeatsEvery *com.example.habits9.data.EnhancedFrequency  toDatabaseValues *com.example.habits9.data.EnhancedFrequency  toDisplayString *com.example.habits9.data.EnhancedFrequency  toString *com.example.habits9.data.EnhancedFrequency  type *com.example.habits9.data.EnhancedFrequency  weekOfMonth *com.example.habits9.data.EnhancedFrequency  DAILY 4com.example.habits9.data.EnhancedFrequency.Companion  DatabaseFrequency 4com.example.habits9.data.EnhancedFrequency.Companion  	DayOfWeek 4com.example.habits9.data.EnhancedFrequency.Companion  EnhancedFrequency 4com.example.habits9.data.EnhancedFrequency.Companion  
FrequencyType 4com.example.habits9.data.EnhancedFrequency.Companion  all 4com.example.habits9.data.EnhancedFrequency.Companion  any 4com.example.habits9.data.EnhancedFrequency.Companion  contains 4com.example.habits9.data.EnhancedFrequency.Companion  	emptyList 4com.example.habits9.data.EnhancedFrequency.Companion  first 4com.example.habits9.data.EnhancedFrequency.Companion  fromDatabaseValues 4com.example.habits9.data.EnhancedFrequency.Companion  
fromString 4com.example.habits9.data.EnhancedFrequency.Companion  	fromValue 4com.example.habits9.data.EnhancedFrequency.Companion  
isNotEmpty 4com.example.habits9.data.EnhancedFrequency.Companion  joinToString 4com.example.habits9.data.EnhancedFrequency.Companion  let 4com.example.habits9.data.EnhancedFrequency.Companion  listOf 4com.example.habits9.data.EnhancedFrequency.Companion  toString 4com.example.habits9.data.EnhancedFrequency.Companion  	Companion &com.example.habits9.data.FrequencyType  DAILY &com.example.habits9.data.FrequencyType  
FrequencyType &com.example.habits9.data.FrequencyType  MONTHLY &com.example.habits9.data.FrequencyType  String &com.example.habits9.data.FrequencyType  WEEKLY &com.example.habits9.data.FrequencyType  
fromString &com.example.habits9.data.FrequencyType  name &com.example.habits9.data.FrequencyType  value &com.example.habits9.data.FrequencyType  values &com.example.habits9.data.FrequencyType  DAILY 0com.example.habits9.data.FrequencyType.Companion  MONTHLY 0com.example.habits9.data.FrequencyType.Companion  WEEKLY 0com.example.habits9.data.FrequencyType.Companion  
fromString 0com.example.habits9.data.FrequencyType.Companion  	HabitType com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  creationDate com.example.habits9.data.Habit  
dayOfMonth com.example.habits9.data.Habit  dayOfWeekInMonth com.example.habits9.data.Habit  
daysOfWeek com.example.habits9.data.Habit  
frequencyType com.example.habits9.data.Habit  fromInt com.example.habits9.data.Habit  	habitType com.example.habits9.data.Habit  id com.example.habits9.data.Habit  name com.example.habits9.data.Habit  numericalHabitType com.example.habits9.data.Habit  repeatsEvery com.example.habits9.data.Habit  
targetType com.example.habits9.data.Habit  targetValue com.example.habits9.data.Habit  type com.example.habits9.data.Habit  unit com.example.habits9.data.Habit  weekOfMonth com.example.habits9.data.Habit  OnConflictStrategy !com.example.habits9.data.HabitDao  deleteHabit !com.example.habits9.data.HabitDao  getAllHabits !com.example.habits9.data.HabitDao  getHabitById !com.example.habits9.data.HabitDao  getHabitByIdSync !com.example.habits9.data.HabitDao  insertHabit !com.example.habits9.data.HabitDao  updateHabit !com.example.habits9.data.HabitDao  
completionDao &com.example.habits9.data.HabitDatabase  habitDao &com.example.habits9.data.HabitDatabase  habitSectionDao &com.example.habits9.data.HabitDatabase  getAllHabits (com.example.habits9.data.HabitRepository  getHabitByIdSync (com.example.habits9.data.HabitRepository  habitDao (com.example.habits9.data.HabitRepository  insertHabit (com.example.habits9.data.HabitRepository  color %com.example.habits9.data.HabitSection  copy %com.example.habits9.data.HabitSection  id %com.example.habits9.data.HabitSection  let %com.example.habits9.data.HabitSection  name %com.example.habits9.data.HabitSection  OnConflictStrategy (com.example.habits9.data.HabitSectionDao  deleteHabitSection (com.example.habits9.data.HabitSectionDao  getAllHabitSections (com.example.habits9.data.HabitSectionDao  insertHabitSection (com.example.habits9.data.HabitSectionDao  updateHabitSection (com.example.habits9.data.HabitSectionDao  updateHabitSections (com.example.habits9.data.HabitSectionDao  deleteHabitSection /com.example.habits9.data.HabitSectionRepository  getAllHabitSections /com.example.habits9.data.HabitSectionRepository  habitSectionDao /com.example.habits9.data.HabitSectionRepository  insertHabitSection /com.example.habits9.data.HabitSectionRepository  updateHabitSection /com.example.habits9.data.HabitSectionRepository  updateHabitSections /com.example.habits9.data.HabitSectionRepository  	Companion "com.example.habits9.data.HabitType  	HabitType "com.example.habits9.data.HabitType  IllegalStateException "com.example.habits9.data.HabitType  Int "com.example.habits9.data.HabitType  	NUMERICAL "com.example.habits9.data.HabitType  YES_NO "com.example.habits9.data.HabitType  fromInt "com.example.habits9.data.HabitType  value "com.example.habits9.data.HabitType  IllegalStateException ,com.example.habits9.data.HabitType.Companion  	NUMERICAL ,com.example.habits9.data.HabitType.Companion  YES_NO ,com.example.habits9.data.HabitType.Companion  fromInt ,com.example.habits9.data.HabitType.Companion  AT_LEAST +com.example.habits9.data.NumericalHabitType  AT_MOST +com.example.habits9.data.NumericalHabitType  	Companion +com.example.habits9.data.NumericalHabitType  IllegalStateException +com.example.habits9.data.NumericalHabitType  Int +com.example.habits9.data.NumericalHabitType  NumericalHabitType +com.example.habits9.data.NumericalHabitType  fromInt +com.example.habits9.data.NumericalHabitType  value +com.example.habits9.data.NumericalHabitType  AT_LEAST 5com.example.habits9.data.NumericalHabitType.Companion  AT_MOST 5com.example.habits9.data.NumericalHabitType.Companion  IllegalStateException 5com.example.habits9.data.NumericalHabitType.Companion  fromInt 5com.example.habits9.data.NumericalHabitType.Companion  ApplicationContext 2com.example.habits9.data.UserPreferencesRepository  Context 2com.example.habits9.data.UserPreferencesRepository  Flow 2com.example.habits9.data.UserPreferencesRepository  Inject 2com.example.habits9.data.UserPreferencesRepository  PreferencesKeys 2com.example.habits9.data.UserPreferencesRepository  String 2com.example.habits9.data.UserPreferencesRepository  context 2com.example.habits9.data.UserPreferencesRepository  	dataStore 2com.example.habits9.data.UserPreferencesRepository  edit 2com.example.habits9.data.UserPreferencesRepository  firstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  map 2com.example.habits9.data.UserPreferencesRepository  stringPreferencesKey 2com.example.habits9.data.UserPreferencesRepository  updateFirstDayOfWeek 2com.example.habits9.data.UserPreferencesRepository  FIRST_DAY_OF_WEEK Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  stringPreferencesKey Bcom.example.habits9.data.UserPreferencesRepository.PreferencesKeys  ApplicationContext com.example.habits9.di  
CompletionDao com.example.habits9.di  CompletionRepository com.example.habits9.di  Context com.example.habits9.di  DatabaseModule com.example.habits9.di  HabitDao com.example.habits9.di  
HabitDatabase com.example.habits9.di  HabitRepository com.example.habits9.di  HabitSectionDao com.example.habits9.di  HabitSectionRepository com.example.habits9.di  	InstallIn com.example.habits9.di  	Migration com.example.habits9.di  Module com.example.habits9.di  Provides com.example.habits9.di  Room com.example.habits9.di  	Singleton com.example.habits9.di  SingletonComponent com.example.habits9.di  SupportSQLiteDatabase com.example.habits9.di  databaseBuilder com.example.habits9.di  java com.example.habits9.di  
trimIndent com.example.habits9.di  CompletionRepository %com.example.habits9.di.DatabaseModule  
HabitDatabase %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  
MIGRATION_3_4 %com.example.habits9.di.DatabaseModule  
MIGRATION_4_5 %com.example.habits9.di.DatabaseModule  
MIGRATION_5_6 %com.example.habits9.di.DatabaseModule  Room %com.example.habits9.di.DatabaseModule  databaseBuilder %com.example.habits9.di.DatabaseModule  java %com.example.habits9.di.DatabaseModule  
trimIndent %com.example.habits9.di.DatabaseModule  Boolean com.example.habits9.ui  
Completion com.example.habits9.ui  CompletionRepository com.example.habits9.ui  DateTimeFormatter com.example.habits9.ui  	DayOfWeek com.example.habits9.ui  Double com.example.habits9.ui  	Exception com.example.habits9.ui  Float com.example.habits9.ui  Habit com.example.habits9.ui  HabitRepository com.example.habits9.ui  HabitScheduler com.example.habits9.ui  	HabitType com.example.habits9.ui  HabitWithCompletions com.example.habits9.ui  
HiltViewModel com.example.habits9.ui  Inject com.example.habits9.ui  Instant com.example.habits9.ui  Int com.example.habits9.ui  
JavaDayOfWeek com.example.habits9.ui  List com.example.habits9.ui  	LocalDate com.example.habits9.ui  Long com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  Map com.example.habits9.ui  MutableStateFlow com.example.habits9.ui  NumericalHabitType com.example.habits9.ui  	StateFlow com.example.habits9.ui  String com.example.habits9.ui  TemporalAdjusters com.example.habits9.ui  	ViewModel com.example.habits9.ui  WeekBoundaryUtils com.example.habits9.ui  
WeekFields com.example.habits9.ui  WeekInfo com.example.habits9.ui  WhileSubscribed com.example.habits9.ui  ZoneId com.example.habits9.ui  _completionValuesState com.example.habits9.ui  _completionsState com.example.habits9.ui  _dialogState com.example.habits9.ui  android com.example.habits9.ui  any com.example.habits9.ui  	associate com.example.habits9.ui  
associateWith com.example.habits9.ui  average com.example.habits9.ui  com com.example.habits9.ui  combine com.example.habits9.ui  completionRepository com.example.habits9.ui  completionsFlow com.example.habits9.ui  
component1 com.example.habits9.ui  
component2 com.example.habits9.ui  count com.example.habits9.ui  	emptyList com.example.habits9.ui  emptyMap com.example.habits9.ui  filter com.example.habits9.ui  
flatMapLatest com.example.habits9.ui  flowOf com.example.habits9.ui  forEach com.example.habits9.ui  getCurrentWeekDates com.example.habits9.ui  
getWeekEnd com.example.habits9.ui  
getWeekNumber com.example.habits9.ui  getWeekStart com.example.habits9.ui  groupBy com.example.habits9.ui  habitRepository com.example.habits9.ui  hideMeasurableHabitDialog com.example.habits9.ui  isHabitScheduled com.example.habits9.ui  
isNotEmpty com.example.habits9.ui  
isNullOrEmpty com.example.habits9.ui  kotlinx com.example.habits9.ui  launch com.example.habits9.ui  map com.example.habits9.ui  mapKeys com.example.habits9.ui  	mapValues com.example.habits9.ui  mutableMapOf com.example.habits9.ui  set com.example.habits9.ui  stateIn com.example.habits9.ui  to com.example.habits9.ui  toDoubleOrNull com.example.habits9.ui  toMutableMap com.example.habits9.ui  completionValues +com.example.habits9.ui.HabitWithCompletions  completions +com.example.habits9.ui.HabitWithCompletions  copy +com.example.habits9.ui.HabitWithCompletions  
currentStreak +com.example.habits9.ui.HabitWithCompletions  habit +com.example.habits9.ui.HabitWithCompletions  
scheduledDays +com.example.habits9.ui.HabitWithCompletions  copy "com.example.habits9.ui.MainUiState  habitsWithCompletions "com.example.habits9.ui.MainUiState  	isLoading "com.example.habits9.ui.MainUiState  measurableDialogCurrentValue "com.example.habits9.ui.MainUiState  measurableDialogDate "com.example.habits9.ui.MainUiState  measurableDialogHabitId "com.example.habits9.ui.MainUiState  measurableDialogHabitName "com.example.habits9.ui.MainUiState  measurableDialogTargetType "com.example.habits9.ui.MainUiState  measurableDialogTargetValue "com.example.habits9.ui.MainUiState  measurableDialogUnit "com.example.habits9.ui.MainUiState  showMeasurableDialog "com.example.habits9.ui.MainUiState  weekInfo "com.example.habits9.ui.MainUiState  Boolean $com.example.habits9.ui.MainViewModel  
Completion $com.example.habits9.ui.MainViewModel  CompletionRepository $com.example.habits9.ui.MainViewModel  DateTimeFormatter $com.example.habits9.ui.MainViewModel  	DayOfWeek $com.example.habits9.ui.MainViewModel  Double $com.example.habits9.ui.MainViewModel  	Exception $com.example.habits9.ui.MainViewModel  Float $com.example.habits9.ui.MainViewModel  Habit $com.example.habits9.ui.MainViewModel  HabitRepository $com.example.habits9.ui.MainViewModel  HabitScheduler $com.example.habits9.ui.MainViewModel  	HabitType $com.example.habits9.ui.MainViewModel  HabitWithCompletions $com.example.habits9.ui.MainViewModel  Inject $com.example.habits9.ui.MainViewModel  Instant $com.example.habits9.ui.MainViewModel  Int $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek $com.example.habits9.ui.MainViewModel  List $com.example.habits9.ui.MainViewModel  	LocalDate $com.example.habits9.ui.MainViewModel  Long $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  Map $com.example.habits9.ui.MainViewModel  MutableStateFlow $com.example.habits9.ui.MainViewModel  NumericalHabitType $com.example.habits9.ui.MainViewModel  	StateFlow $com.example.habits9.ui.MainViewModel  String $com.example.habits9.ui.MainViewModel  TemporalAdjusters $com.example.habits9.ui.MainViewModel  WeekBoundaryUtils $com.example.habits9.ui.MainViewModel  
WeekFields $com.example.habits9.ui.MainViewModel  WeekInfo $com.example.habits9.ui.MainViewModel  WhileSubscribed $com.example.habits9.ui.MainViewModel  ZoneId $com.example.habits9.ui.MainViewModel  _completionValuesState $com.example.habits9.ui.MainViewModel  _completionsState $com.example.habits9.ui.MainViewModel  _dialogState $com.example.habits9.ui.MainViewModel  android $com.example.habits9.ui.MainViewModel  any $com.example.habits9.ui.MainViewModel  	associate $com.example.habits9.ui.MainViewModel  
associateWith $com.example.habits9.ui.MainViewModel  average $com.example.habits9.ui.MainViewModel  calculateCurrentStreak $com.example.habits9.ui.MainViewModel  #calculateDailyCompletionPercentages $com.example.habits9.ui.MainViewModel  calculateScheduledDays $com.example.habits9.ui.MainViewModel  calculateWeekInfo $com.example.habits9.ui.MainViewModel  com $com.example.habits9.ui.MainViewModel  combine $com.example.habits9.ui.MainViewModel  completionRepository $com.example.habits9.ui.MainViewModel  completionsFlow $com.example.habits9.ui.MainViewModel  
component1 $com.example.habits9.ui.MainViewModel  
component2 $com.example.habits9.ui.MainViewModel  count $com.example.habits9.ui.MainViewModel  	emptyList $com.example.habits9.ui.MainViewModel  emptyMap $com.example.habits9.ui.MainViewModel  enhancedUiState $com.example.habits9.ui.MainViewModel  filter $com.example.habits9.ui.MainViewModel  filterScheduledHabits $com.example.habits9.ui.MainViewModel  
flatMapLatest $com.example.habits9.ui.MainViewModel  flowOf $com.example.habits9.ui.MainViewModel  getCurrentWeekDates $com.example.habits9.ui.MainViewModel  getCurrentWeekStart $com.example.habits9.ui.MainViewModel  
getWeekEnd $com.example.habits9.ui.MainViewModel  
getWeekNumber $com.example.habits9.ui.MainViewModel  getWeekStart $com.example.habits9.ui.MainViewModel  groupBy $com.example.habits9.ui.MainViewModel  habitRepository $com.example.habits9.ui.MainViewModel  hideMeasurableHabitDialog $com.example.habits9.ui.MainViewModel  isHabitScheduled $com.example.habits9.ui.MainViewModel  
isNotEmpty $com.example.habits9.ui.MainViewModel  
isNullOrEmpty $com.example.habits9.ui.MainViewModel  kotlinx $com.example.habits9.ui.MainViewModel  launch $com.example.habits9.ui.MainViewModel  map $com.example.habits9.ui.MainViewModel  mapKeys $com.example.habits9.ui.MainViewModel  	mapValues $com.example.habits9.ui.MainViewModel  mutableMapOf $com.example.habits9.ui.MainViewModel  saveMeasurableHabitCompletion $com.example.habits9.ui.MainViewModel  set $com.example.habits9.ui.MainViewModel  showMeasurableHabitDialog $com.example.habits9.ui.MainViewModel  stateIn $com.example.habits9.ui.MainViewModel  to $com.example.habits9.ui.MainViewModel  toDoubleOrNull $com.example.habits9.ui.MainViewModel  toMutableMap $com.example.habits9.ui.MainViewModel  toggleCompletion $com.example.habits9.ui.MainViewModel  uiState $com.example.habits9.ui.MainViewModel  updateMeasurableDialogValue $com.example.habits9.ui.MainViewModel  userPreferencesRepository $com.example.habits9.ui.MainViewModel  viewModelScope $com.example.habits9.ui.MainViewModel  
JavaDayOfWeek 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  TemporalAdjusters 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
WeekFields 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getCurrentWeekDates 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekEnd 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekNumber 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getWeekStart 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  map 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  example (com.example.habits9.ui.MainViewModel.com  habits9 0com.example.habits9.ui.MainViewModel.com.example  data 8com.example.habits9.ui.MainViewModel.com.example.habits9  Habit =com.example.habits9.ui.MainViewModel.com.example.habits9.data  UserPreferencesRepository =com.example.habits9.ui.MainViewModel.com.example.habits9.data  dailyCompletionPercentages com.example.habits9.ui.WeekInfo  dates com.example.habits9.ui.WeekInfo  firstDayOfWeek com.example.habits9.ui.WeekInfo  formattedDates com.example.habits9.ui.WeekInfo  formattedDayAbbreviations com.example.habits9.ui.WeekInfo  
timestamps com.example.habits9.ui.WeekInfo  
weekNumber com.example.habits9.ui.WeekInfo  weeklyCompletionPercentage com.example.habits9.ui.WeekInfo  example com.example.habits9.ui.com  habits9 "com.example.habits9.ui.com.example  data *com.example.habits9.ui.com.example.habits9  Habit /com.example.habits9.ui.com.example.habits9.data  UserPreferencesRepository /com.example.habits9.ui.com.example.habits9.data  
AccentPrimary !com.example.habits9.ui.components  AlertDialog !com.example.habits9.ui.components  	Alignment !com.example.habits9.ui.components  Arrangement !com.example.habits9.ui.components  BackgroundDark !com.example.habits9.ui.components  Boolean !com.example.habits9.ui.components  BorderStroke !com.example.habits9.ui.components  Box !com.example.habits9.ui.components  Button !com.example.habits9.ui.components  ButtonDefaults !com.example.habits9.ui.components  Card !com.example.habits9.ui.components  CardDefaults !com.example.habits9.ui.components  Column !com.example.habits9.ui.components  
Composable !com.example.habits9.ui.components  DarkBackground !com.example.habits9.ui.components  DayChip !com.example.habits9.ui.components  	DayOfWeek !com.example.habits9.ui.components  DividerColor !com.example.habits9.ui.components  Double !com.example.habits9.ui.components  EnhancedFrequency !com.example.habits9.ui.components  EnhancedFrequencyPickerDialog !com.example.habits9.ui.components  ExperimentalMaterial3Api !com.example.habits9.ui.components  
FontWeight !com.example.habits9.ui.components  
FrequencyType !com.example.habits9.ui.components  FrequencyTypeTab !com.example.habits9.ui.components  	ImeAction !com.example.habits9.ui.components  Int !com.example.habits9.ui.components  KeyboardActions !com.example.habits9.ui.components  KeyboardOptions !com.example.habits9.ui.components  KeyboardType !com.example.habits9.ui.components  LaunchedEffect !com.example.habits9.ui.components  LazyRow !com.example.habits9.ui.components  
MaterialTheme !com.example.habits9.ui.components  Modifier !com.example.habits9.ui.components  MonthlyOptions !com.example.habits9.ui.components  NumericalInputDialog !com.example.habits9.ui.components  OptIn !com.example.habits9.ui.components  OutlinedButton !com.example.habits9.ui.components  OutlinedTextField !com.example.habits9.ui.components  OutlinedTextFieldDefaults !com.example.habits9.ui.components  Row !com.example.habits9.ui.components  Set !com.example.habits9.ui.components  Spacer !com.example.habits9.ui.components  String !com.example.habits9.ui.components  SurfaceVariantDark !com.example.habits9.ui.components  Text !com.example.habits9.ui.components  	TextAlign !com.example.habits9.ui.components  
TextButton !com.example.habits9.ui.components  TextPrimary !com.example.habits9.ui.components  
TextSecondary !com.example.habits9.ui.components  Unit !com.example.habits9.ui.components  
WeeklyOptions !com.example.habits9.ui.components  buttonColors !com.example.habits9.ui.components  
cardColors !com.example.habits9.ui.components  colors !com.example.habits9.ui.components  fillMaxWidth !com.example.habits9.ui.components  focusRequester !com.example.habits9.ui.components  forEach !com.example.habits9.ui.components  getValue !com.example.habits9.ui.components  height !com.example.habits9.ui.components  
isNotEmpty !com.example.habits9.ui.components  let !com.example.habits9.ui.components  	lowercase !com.example.habits9.ui.components  minus !com.example.habits9.ui.components  mutableStateOf !com.example.habits9.ui.components  outlinedButtonColors !com.example.habits9.ui.components  padding !com.example.habits9.ui.components  plus !com.example.habits9.ui.components  provideDelegate !com.example.habits9.ui.components  remember !com.example.habits9.ui.components  replaceFirstChar !com.example.habits9.ui.components  setOf !com.example.habits9.ui.components  setValue !com.example.habits9.ui.components  size !com.example.habits9.ui.components  spacedBy !com.example.habits9.ui.components  take !com.example.habits9.ui.components  toDoubleOrNull !com.example.habits9.ui.components  toIntOrNull !com.example.habits9.ui.components  toList !com.example.habits9.ui.components  toSet !com.example.habits9.ui.components  	uppercase !com.example.habits9.ui.components  weight !com.example.habits9.ui.components  width !com.example.habits9.ui.components  Boolean "com.example.habits9.ui.createhabit  CreateHabitUiState "com.example.habits9.ui.createhabit  CreateHabitViewModel "com.example.habits9.ui.createhabit  	DayOfWeek "com.example.habits9.ui.createhabit  EnhancedFrequency "com.example.habits9.ui.createhabit  	Exception "com.example.habits9.ui.createhabit  HabitFrequency "com.example.habits9.ui.createhabit  HabitSection "com.example.habits9.ui.createhabit  HabitSectionRepository "com.example.habits9.ui.createhabit  
HiltViewModel "com.example.habits9.ui.createhabit  Inject "com.example.habits9.ui.createhabit  Int "com.example.habits9.ui.createhabit  List "com.example.habits9.ui.createhabit  	LocalTime "com.example.habits9.ui.createhabit  MutableStateFlow "com.example.habits9.ui.createhabit  
ReminderState "com.example.habits9.ui.createhabit  Set "com.example.habits9.ui.createhabit  	StateFlow "com.example.habits9.ui.createhabit  String "com.example.habits9.ui.createhabit  Unit "com.example.habits9.ui.createhabit  	ViewModel "com.example.habits9.ui.createhabit  _uiState "com.example.habits9.ui.createhabit  asStateFlow "com.example.habits9.ui.createhabit  com "com.example.habits9.ui.createhabit  	emptyList "com.example.habits9.ui.createhabit  first "com.example.habits9.ui.createhabit  format "com.example.habits9.ui.createhabit  habitRepository "com.example.habits9.ui.createhabit  habitSectionRepository "com.example.habits9.ui.createhabit  isBlank "com.example.habits9.ui.createhabit  
isNotEmpty "com.example.habits9.ui.createhabit  joinToString "com.example.habits9.ui.createhabit  launch "com.example.habits9.ui.createhabit  	lowercase "com.example.habits9.ui.createhabit  map "com.example.habits9.ui.createhabit  replaceFirstChar "com.example.habits9.ui.createhabit  setOf "com.example.habits9.ui.createhabit  sortedBy "com.example.habits9.ui.createhabit  toDoubleOrNull "com.example.habits9.ui.createhabit  	uppercase "com.example.habits9.ui.createhabit  availableSections 5com.example.habits9.ui.createhabit.CreateHabitUiState  copy 5com.example.habits9.ui.createhabit.CreateHabitUiState  enhancedFrequency 5com.example.habits9.ui.createhabit.CreateHabitUiState  
reminderState 5com.example.habits9.ui.createhabit.CreateHabitUiState  
selectedColor 5com.example.habits9.ui.createhabit.CreateHabitUiState  selectedSection 5com.example.habits9.ui.createhabit.CreateHabitUiState  showReminderDialog 5com.example.habits9.ui.createhabit.CreateHabitUiState  showSectionSelector 5com.example.habits9.ui.createhabit.CreateHabitUiState  CreateHabitUiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  MutableStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  _uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  asStateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  com 7com.example.habits9.ui.createhabit.CreateHabitViewModel  first 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  habitSectionRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  hideSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  isBlank 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
isNotEmpty 7com.example.habits9.ui.createhabit.CreateHabitViewModel  launch 7com.example.habits9.ui.createhabit.CreateHabitViewModel  loadSections 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	saveHabit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
selectSection 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showReminderDialog 7com.example.habits9.ui.createhabit.CreateHabitViewModel  showSectionSelector 7com.example.habits9.ui.createhabit.CreateHabitViewModel  toDoubleOrNull 7com.example.habits9.ui.createhabit.CreateHabitViewModel  uiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateEnhancedFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  updateReminder 7com.example.habits9.ui.createhabit.CreateHabitViewModel  viewModelScope 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	Companion 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY 1com.example.habits9.ui.createhabit.HabitFrequency  EnhancedFrequency 1com.example.habits9.ui.createhabit.HabitFrequency  
FrequencyType 1com.example.habits9.ui.createhabit.HabitFrequency  HabitFrequency 1com.example.habits9.ui.createhabit.HabitFrequency  Int 1com.example.habits9.ui.createhabit.HabitFrequency  String 1com.example.habits9.ui.createhabit.HabitFrequency  denominator 1com.example.habits9.ui.createhabit.HabitFrequency  	emptyList 1com.example.habits9.ui.createhabit.HabitFrequency  	numerator 1com.example.habits9.ui.createhabit.HabitFrequency  DAILY ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  HabitFrequency ;com.example.habits9.ui.createhabit.HabitFrequency.Companion  	DayOfWeek 0com.example.habits9.ui.createhabit.ReminderState  String 0com.example.habits9.ui.createhabit.ReminderState  first 0com.example.habits9.ui.createhabit.ReminderState  format 0com.example.habits9.ui.createhabit.ReminderState  	isEnabled 0com.example.habits9.ui.createhabit.ReminderState  joinToString 0com.example.habits9.ui.createhabit.ReminderState  	lowercase 0com.example.habits9.ui.createhabit.ReminderState  map 0com.example.habits9.ui.createhabit.ReminderState  replaceFirstChar 0com.example.habits9.ui.createhabit.ReminderState  selectedDays 0com.example.habits9.ui.createhabit.ReminderState  sortedBy 0com.example.habits9.ui.createhabit.ReminderState  time 0com.example.habits9.ui.createhabit.ReminderState  toDisplayString 0com.example.habits9.ui.createhabit.ReminderState  	uppercase 0com.example.habits9.ui.createhabit.ReminderState  example &com.example.habits9.ui.createhabit.com  habits9 .com.example.habits9.ui.createhabit.com.example  data 6com.example.habits9.ui.createhabit.com.example.habits9  HabitRepository ;com.example.habits9.ui.createhabit.com.example.habits9.data  	HabitType ;com.example.habits9.ui.createhabit.com.example.habits9.data  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  AlertDialog ,com.example.habits9.ui.createmeasurablehabit  	Alignment ,com.example.habits9.ui.createmeasurablehabit  Arrangement ,com.example.habits9.ui.createmeasurablehabit  Box ,com.example.habits9.ui.createmeasurablehabit  CircleShape ,com.example.habits9.ui.createmeasurablehabit  Color ,com.example.habits9.ui.createmeasurablehabit  Column ,com.example.habits9.ui.createmeasurablehabit  
Composable ,com.example.habits9.ui.createmeasurablehabit  CreateHabitViewModel ,com.example.habits9.ui.createmeasurablehabit  CreateMeasurableHabitScreen ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  	DayOfWeek ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  DropdownMenuItem ,com.example.habits9.ui.createmeasurablehabit  ExperimentalMaterial3Api ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuBox ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuDefaults ,com.example.habits9.ui.createmeasurablehabit  
FontWeight ,com.example.habits9.ui.createmeasurablehabit  Icon ,com.example.habits9.ui.createmeasurablehabit  
IconButton ,com.example.habits9.ui.createmeasurablehabit  Icons ,com.example.habits9.ui.createmeasurablehabit  LaunchedEffect ,com.example.habits9.ui.createmeasurablehabit  List ,com.example.habits9.ui.createmeasurablehabit  	LocalTime ,com.example.habits9.ui.createmeasurablehabit  Modifier ,com.example.habits9.ui.createmeasurablehabit  OptIn ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextField ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextFieldDefaults ,com.example.habits9.ui.createmeasurablehabit  ReminderDialog ,com.example.habits9.ui.createmeasurablehabit  
ReminderState ,com.example.habits9.ui.createmeasurablehabit  Row ,com.example.habits9.ui.createmeasurablehabit  Scaffold ,com.example.habits9.ui.createmeasurablehabit  SectionSelectorDialog ,com.example.habits9.ui.createmeasurablehabit  Spacer ,com.example.habits9.ui.createmeasurablehabit  String ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  Switch ,com.example.habits9.ui.createmeasurablehabit  SwitchDefaults ,com.example.habits9.ui.createmeasurablehabit  Text ,com.example.habits9.ui.createmeasurablehabit  
TextButton ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  
TimePicker ,com.example.habits9.ui.createmeasurablehabit  TimePickerDefaults ,com.example.habits9.ui.createmeasurablehabit  TimePickerDialog ,com.example.habits9.ui.createmeasurablehabit  	TopAppBar ,com.example.habits9.ui.createmeasurablehabit  TopAppBarDefaults ,com.example.habits9.ui.createmeasurablehabit  TrailingIcon ,com.example.habits9.ui.createmeasurablehabit  Unit ,com.example.habits9.ui.createmeasurablehabit  
background ,com.example.habits9.ui.createmeasurablehabit  	clickable ,com.example.habits9.ui.createmeasurablehabit  collectAsState ,com.example.habits9.ui.createmeasurablehabit  colors ,com.example.habits9.ui.createmeasurablehabit  com ,com.example.habits9.ui.createmeasurablehabit  fillMaxSize ,com.example.habits9.ui.createmeasurablehabit  fillMaxWidth ,com.example.habits9.ui.createmeasurablehabit  forEach ,com.example.habits9.ui.createmeasurablehabit  format ,com.example.habits9.ui.createmeasurablehabit  getValue ,com.example.habits9.ui.createmeasurablehabit  height ,com.example.habits9.ui.createmeasurablehabit  
isNotEmpty ,com.example.habits9.ui.createmeasurablehabit  kotlinx ,com.example.habits9.ui.createmeasurablehabit  listOf ,com.example.habits9.ui.createmeasurablehabit  minus ,com.example.habits9.ui.createmeasurablehabit  mutableStateOf ,com.example.habits9.ui.createmeasurablehabit  padding ,com.example.habits9.ui.createmeasurablehabit  plus ,com.example.habits9.ui.createmeasurablehabit  provideDelegate ,com.example.habits9.ui.createmeasurablehabit  remember ,com.example.habits9.ui.createmeasurablehabit  rememberTimePickerState ,com.example.habits9.ui.createmeasurablehabit  setValue ,com.example.habits9.ui.createmeasurablehabit  size ,com.example.habits9.ui.createmeasurablehabit  to ,com.example.habits9.ui.createmeasurablehabit  topAppBarColors ,com.example.habits9.ui.createmeasurablehabit  weight ,com.example.habits9.ui.createmeasurablehabit  width ,com.example.habits9.ui.createmeasurablehabit  example 0com.example.habits9.ui.createmeasurablehabit.com  habits9 8com.example.habits9.ui.createmeasurablehabit.com.example  data @com.example.habits9.ui.createmeasurablehabit.com.example.habits9  HabitSection Ecom.example.habits9.ui.createmeasurablehabit.com.example.habits9.data  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  AlertDialog 'com.example.habits9.ui.createyesnohabit  	Alignment 'com.example.habits9.ui.createyesnohabit  Arrangement 'com.example.habits9.ui.createyesnohabit  Box 'com.example.habits9.ui.createyesnohabit  CircleShape 'com.example.habits9.ui.createyesnohabit  Color 'com.example.habits9.ui.createyesnohabit  Column 'com.example.habits9.ui.createyesnohabit  
Composable 'com.example.habits9.ui.createyesnohabit  CreateHabitViewModel 'com.example.habits9.ui.createyesnohabit  CreateYesNoHabitScreen 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  	DayOfWeek 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  ExperimentalMaterial3Api 'com.example.habits9.ui.createyesnohabit  
FontWeight 'com.example.habits9.ui.createyesnohabit  Icon 'com.example.habits9.ui.createyesnohabit  
IconButton 'com.example.habits9.ui.createyesnohabit  Icons 'com.example.habits9.ui.createyesnohabit  LaunchedEffect 'com.example.habits9.ui.createyesnohabit  List 'com.example.habits9.ui.createyesnohabit  	LocalTime 'com.example.habits9.ui.createyesnohabit  Modifier 'com.example.habits9.ui.createyesnohabit  OptIn 'com.example.habits9.ui.createyesnohabit  OutlinedTextField 'com.example.habits9.ui.createyesnohabit  OutlinedTextFieldDefaults 'com.example.habits9.ui.createyesnohabit  ReminderDialog 'com.example.habits9.ui.createyesnohabit  
ReminderState 'com.example.habits9.ui.createyesnohabit  Row 'com.example.habits9.ui.createyesnohabit  Scaffold 'com.example.habits9.ui.createyesnohabit  SectionSelectorDialog 'com.example.habits9.ui.createyesnohabit  Spacer 'com.example.habits9.ui.createyesnohabit  String 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  Switch 'com.example.habits9.ui.createyesnohabit  SwitchDefaults 'com.example.habits9.ui.createyesnohabit  Text 'com.example.habits9.ui.createyesnohabit  
TextButton 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  
TimePicker 'com.example.habits9.ui.createyesnohabit  TimePickerDefaults 'com.example.habits9.ui.createyesnohabit  TimePickerDialog 'com.example.habits9.ui.createyesnohabit  	TopAppBar 'com.example.habits9.ui.createyesnohabit  TopAppBarDefaults 'com.example.habits9.ui.createyesnohabit  Unit 'com.example.habits9.ui.createyesnohabit  
background 'com.example.habits9.ui.createyesnohabit  	clickable 'com.example.habits9.ui.createyesnohabit  collectAsState 'com.example.habits9.ui.createyesnohabit  colors 'com.example.habits9.ui.createyesnohabit  com 'com.example.habits9.ui.createyesnohabit  fillMaxSize 'com.example.habits9.ui.createyesnohabit  fillMaxWidth 'com.example.habits9.ui.createyesnohabit  forEach 'com.example.habits9.ui.createyesnohabit  format 'com.example.habits9.ui.createyesnohabit  getValue 'com.example.habits9.ui.createyesnohabit  height 'com.example.habits9.ui.createyesnohabit  
isNotEmpty 'com.example.habits9.ui.createyesnohabit  kotlinx 'com.example.habits9.ui.createyesnohabit  listOf 'com.example.habits9.ui.createyesnohabit  minus 'com.example.habits9.ui.createyesnohabit  mutableStateOf 'com.example.habits9.ui.createyesnohabit  padding 'com.example.habits9.ui.createyesnohabit  plus 'com.example.habits9.ui.createyesnohabit  provideDelegate 'com.example.habits9.ui.createyesnohabit  remember 'com.example.habits9.ui.createyesnohabit  rememberTimePickerState 'com.example.habits9.ui.createyesnohabit  setValue 'com.example.habits9.ui.createyesnohabit  size 'com.example.habits9.ui.createyesnohabit  to 'com.example.habits9.ui.createyesnohabit  topAppBarColors 'com.example.habits9.ui.createyesnohabit  weight 'com.example.habits9.ui.createyesnohabit  width 'com.example.habits9.ui.createyesnohabit  example +com.example.habits9.ui.createyesnohabit.com  habits9 3com.example.habits9.ui.createyesnohabit.com.example  data ;com.example.habits9.ui.createyesnohabit.com.example.habits9  HabitSection @com.example.habits9.ui.createyesnohabit.com.example.habits9.data  
AccentPrimary com.example.habits9.ui.details  	Alignment com.example.habits9.ui.details  Arrangement com.example.habits9.ui.details  Build com.example.habits9.ui.details  CircularProgressIndicator com.example.habits9.ui.details  
Composable com.example.habits9.ui.details  DarkBackground com.example.habits9.ui.details  DividerColor com.example.habits9.ui.details  ExperimentalMaterial3Api com.example.habits9.ui.details  Float com.example.habits9.ui.details  
FontFamily com.example.habits9.ui.details  
FontWeight com.example.habits9.ui.details  HabitDetailsScreen com.example.habits9.ui.details  Icon com.example.habits9.ui.details  
IconButton com.example.habits9.ui.details  Icons com.example.habits9.ui.details  
MetricView com.example.habits9.ui.details  Modifier com.example.habits9.ui.details  OptIn com.example.habits9.ui.details  OverviewSection com.example.habits9.ui.details  RequiresApi com.example.habits9.ui.details  Row com.example.habits9.ui.details  Spacer com.example.habits9.ui.details  String com.example.habits9.ui.details  SubHeaderRow com.example.habits9.ui.details  SurfaceVariantDark com.example.habits9.ui.details  Text com.example.habits9.ui.details  TextPrimary com.example.habits9.ui.details  
TextSecondary com.example.habits9.ui.details  TopAppBarDefaults com.example.habits9.ui.details  Unit com.example.habits9.ui.details  fillMaxSize com.example.habits9.ui.details  fillMaxWidth com.example.habits9.ui.details  height com.example.habits9.ui.details  size com.example.habits9.ui.details  topAppBarColors com.example.habits9.ui.details  width com.example.habits9.ui.details  
AccentPrimary )com.example.habits9.ui.habittypeselection  	Alignment )com.example.habits9.ui.habittypeselection  Arrangement )com.example.habits9.ui.habittypeselection  Card )com.example.habits9.ui.habittypeselection  CardDefaults )com.example.habits9.ui.habittypeselection  Column )com.example.habits9.ui.habittypeselection  
Composable )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  ExperimentalMaterial3Api )com.example.habits9.ui.habittypeselection  
FontWeight )com.example.habits9.ui.habittypeselection  HabitTypeSelectionScreen )com.example.habits9.ui.habittypeselection  Icon )com.example.habits9.ui.habittypeselection  
IconButton )com.example.habits9.ui.habittypeselection  Icons )com.example.habits9.ui.habittypeselection  Modifier )com.example.habits9.ui.habittypeselection  OptIn )com.example.habits9.ui.habittypeselection  RoundedCornerShape )com.example.habits9.ui.habittypeselection  Scaffold )com.example.habits9.ui.habittypeselection  Spacer )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  Text )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  	TopAppBar )com.example.habits9.ui.habittypeselection  TopAppBarDefaults )com.example.habits9.ui.habittypeselection  Unit )com.example.habits9.ui.habittypeselection  
cardColors )com.example.habits9.ui.habittypeselection  	clickable )com.example.habits9.ui.habittypeselection  fillMaxSize )com.example.habits9.ui.habittypeselection  fillMaxWidth )com.example.habits9.ui.habittypeselection  height )com.example.habits9.ui.habittypeselection  padding )com.example.habits9.ui.habittypeselection  topAppBarColors )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  	Alignment com.example.habits9.ui.home  Arrangement com.example.habits9.ui.home  BackgroundDark com.example.habits9.ui.home  Boolean com.example.habits9.ui.home  Box com.example.habits9.ui.home  Build com.example.habits9.ui.home  CircleShape com.example.habits9.ui.home  Color com.example.habits9.ui.home  Column com.example.habits9.ui.home  CompletionIndicator com.example.habits9.ui.home  
Composable com.example.habits9.ui.home  CustomHeader com.example.habits9.ui.home  	DayOfWeek com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  DropdownMenu com.example.habits9.ui.home  DropdownMenuItem com.example.habits9.ui.home  ExperimentalMaterial3Api com.example.habits9.ui.home  
FontFamily com.example.habits9.ui.home  
FontWeight com.example.habits9.ui.home  FrozenPaneLayout com.example.habits9.ui.home  
HomeScreen com.example.habits9.ui.home  Icon com.example.habits9.ui.home  
IconButton com.example.habits9.ui.home  Icons com.example.habits9.ui.home  
LazyColumn com.example.habits9.ui.home  List com.example.habits9.ui.home  	LocalDate com.example.habits9.ui.home  Long com.example.habits9.ui.home  Modifier com.example.habits9.ui.home  Offset com.example.habits9.ui.home  OptIn com.example.habits9.ui.home  RequiresApi com.example.habits9.ui.home  Row com.example.habits9.ui.home  Spacer com.example.habits9.ui.home  String com.example.habits9.ui.home  Stroke com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  Text com.example.habits9.ui.home  	TextAlign com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  TopAppBarDefaults com.example.habits9.ui.home  Unit com.example.habits9.ui.home  alpha com.example.habits9.ui.home  android com.example.habits9.ui.home  
background com.example.habits9.ui.home  	clickable com.example.habits9.ui.home  com com.example.habits9.ui.home  
drawBehind com.example.habits9.ui.home  fillMaxSize com.example.habits9.ui.home  fillMaxWidth com.example.habits9.ui.home  forEachIndexed com.example.habits9.ui.home  height com.example.habits9.ui.home  horizontalScroll com.example.habits9.ui.home  
isNotEmpty com.example.habits9.ui.home  
isNullOrEmpty com.example.habits9.ui.home  isWeekStartDay com.example.habits9.ui.home  padding com.example.habits9.ui.home  provideDelegate com.example.habits9.ui.home  size com.example.habits9.ui.home  spacedBy com.example.habits9.ui.home  take com.example.habits9.ui.home  toDoubleOrNull com.example.habits9.ui.home  topAppBarColors com.example.habits9.ui.home  weight com.example.habits9.ui.home  width com.example.habits9.ui.home  example com.example.habits9.ui.home.com  habits9 'com.example.habits9.ui.home.com.example  ui /com.example.habits9.ui.home.com.example.habits9  HabitWithCompletions 2com.example.habits9.ui.home.com.example.habits9.ui  
MainViewModel 2com.example.habits9.ui.home.com.example.habits9.ui  WeekInfo 2com.example.habits9.ui.home.com.example.habits9.ui  
AccentPrimary %com.example.habits9.ui.managesections  AlertDialog %com.example.habits9.ui.managesections  	Alignment %com.example.habits9.ui.managesections  Arrangement %com.example.habits9.ui.managesections  Boolean %com.example.habits9.ui.managesections  Box %com.example.habits9.ui.managesections  Card %com.example.habits9.ui.managesections  CardDefaults %com.example.habits9.ui.managesections  CircleShape %com.example.habits9.ui.managesections  CircularProgressIndicator %com.example.habits9.ui.managesections  Color %com.example.habits9.ui.managesections  Column %com.example.habits9.ui.managesections  
Composable %com.example.habits9.ui.managesections  CreateSectionDialog %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DeleteSectionDialog %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  EditSectionDialog %com.example.habits9.ui.managesections  ExperimentalMaterial3Api %com.example.habits9.ui.managesections  
FontWeight %com.example.habits9.ui.managesections  GhostPlaceholder %com.example.habits9.ui.managesections  HabitSection %com.example.habits9.ui.managesections  HabitSectionRepository %com.example.habits9.ui.managesections  HapticFeedbackType %com.example.habits9.ui.managesections  
HiltViewModel %com.example.habits9.ui.managesections  Icon %com.example.habits9.ui.managesections  
IconButton %com.example.habits9.ui.managesections  Icons %com.example.habits9.ui.managesections  Inject %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  
LazyColumn %com.example.habits9.ui.managesections  LazyRow %com.example.habits9.ui.managesections  List %com.example.habits9.ui.managesections  ManageSectionsScreen %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  Modifier %com.example.habits9.ui.managesections  MutableStateFlow %com.example.habits9.ui.managesections  Offset %com.example.habits9.ui.managesections  OptIn %com.example.habits9.ui.managesections  OutlinedTextField %com.example.habits9.ui.managesections  OutlinedTextFieldDefaults %com.example.habits9.ui.managesections  
PaddingValues %com.example.habits9.ui.managesections  RoundedCornerShape %com.example.habits9.ui.managesections  Row %com.example.habits9.ui.managesections  Scaffold %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  SectionListItem %com.example.habits9.ui.managesections  Spacer %com.example.habits9.ui.managesections  	StateFlow %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  Text %com.example.habits9.ui.managesections  
TextButton %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  	TopAppBar %com.example.habits9.ui.managesections  TopAppBarDefaults %com.example.habits9.ui.managesections  Unit %com.example.habits9.ui.managesections  	ViewModel %com.example.habits9.ui.managesections  _uiState %com.example.habits9.ui.managesections  align %com.example.habits9.ui.managesections  alpha %com.example.habits9.ui.managesections  animateContentSize %com.example.habits9.ui.managesections  asStateFlow %com.example.habits9.ui.managesections  
background %com.example.habits9.ui.managesections  
cardColors %com.example.habits9.ui.managesections  	clickable %com.example.habits9.ui.managesections  coerceAtMost %com.example.habits9.ui.managesections  collectAsState %com.example.habits9.ui.managesections  colors %com.example.habits9.ui.managesections  	emptyList %com.example.habits9.ui.managesections  fillMaxSize %com.example.habits9.ui.managesections  fillMaxWidth %com.example.habits9.ui.managesections  find %com.example.habits9.ui.managesections  forEachIndexed %com.example.habits9.ui.managesections  getValue %com.example.habits9.ui.managesections  
graphicsLayer %com.example.habits9.ui.managesections  habitSectionRepository %com.example.habits9.ui.managesections  height %com.example.habits9.ui.managesections  hideCreateDialog %com.example.habits9.ui.managesections  hideDeleteDialog %com.example.habits9.ui.managesections  hideEditDialog %com.example.habits9.ui.managesections  indexOfFirst %com.example.habits9.ui.managesections  isBlank %com.example.habits9.ui.managesections  
isNotBlank %com.example.habits9.ui.managesections  launch %com.example.habits9.ui.managesections  let %com.example.habits9.ui.managesections  listOf %com.example.habits9.ui.managesections  
mapIndexed %com.example.habits9.ui.managesections  mutableStateOf %com.example.habits9.ui.managesections  padding %com.example.habits9.ui.managesections  
plusAssign %com.example.habits9.ui.managesections  pointerInput %com.example.habits9.ui.managesections  provideDelegate %com.example.habits9.ui.managesections  remember %com.example.habits9.ui.managesections  rememberCoroutineScope %com.example.habits9.ui.managesections  setValue %com.example.habits9.ui.managesections  shadow %com.example.habits9.ui.managesections  size %com.example.habits9.ui.managesections  spacedBy %com.example.habits9.ui.managesections  takeIf %com.example.habits9.ui.managesections  
toMutableList %com.example.habits9.ui.managesections  topAppBarColors %com.example.habits9.ui.managesections  trim %com.example.habits9.ui.managesections  weight %com.example.habits9.ui.managesections  width %com.example.habits9.ui.managesections  zIndex %com.example.habits9.ui.managesections  copy ;com.example.habits9.ui.managesections.ManageSectionsUiState  deletingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  editingSection ;com.example.habits9.ui.managesections.ManageSectionsUiState  	isLoading ;com.example.habits9.ui.managesections.ManageSectionsUiState  sections ;com.example.habits9.ui.managesections.ManageSectionsUiState  showCreateDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showDeleteDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  showEditDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  MutableStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  _uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  asStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  
createSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  
deleteSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  habitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  isBlank =com.example.habits9.ui.managesections.ManageSectionsViewModel  launch =com.example.habits9.ui.managesections.ManageSectionsViewModel  loadSections =com.example.habits9.ui.managesections.ManageSectionsViewModel  
mapIndexed =com.example.habits9.ui.managesections.ManageSectionsViewModel  onSectionMoved =com.example.habits9.ui.managesections.ManageSectionsViewModel  showCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showDeleteDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  showEditDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  
toMutableList =com.example.habits9.ui.managesections.ManageSectionsViewModel  trim =com.example.habits9.ui.managesections.ManageSectionsViewModel  uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  
updateSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  viewModelScope =com.example.habits9.ui.managesections.ManageSectionsViewModel  
AccentPrimary com.example.habits9.ui.settings  	Alignment com.example.habits9.ui.settings  Column com.example.habits9.ui.settings  
Composable com.example.habits9.ui.settings  DarkBackground com.example.habits9.ui.settings  ExperimentalMaterial3Api com.example.habits9.ui.settings  
FontFamily com.example.habits9.ui.settings  
FontWeight com.example.habits9.ui.settings  
HiltViewModel com.example.habits9.ui.settings  Icons com.example.habits9.ui.settings  Inject com.example.habits9.ui.settings  Modifier com.example.habits9.ui.settings  OptIn com.example.habits9.ui.settings  RadioButton com.example.habits9.ui.settings  RadioButtonDefaults com.example.habits9.ui.settings  Role com.example.habits9.ui.settings  Row com.example.habits9.ui.settings  SettingsScreen com.example.habits9.ui.settings  SettingsViewModel com.example.habits9.ui.settings  SharingStarted com.example.habits9.ui.settings  Spacer com.example.habits9.ui.settings  	StateFlow com.example.habits9.ui.settings  String com.example.habits9.ui.settings  SurfaceVariantDark com.example.habits9.ui.settings  Text com.example.habits9.ui.settings  TextPrimary com.example.habits9.ui.settings  
TextSecondary com.example.habits9.ui.settings  TopAppBarDefaults com.example.habits9.ui.settings  Unit com.example.habits9.ui.settings  UserPreferencesRepository com.example.habits9.ui.settings  	ViewModel com.example.habits9.ui.settings  WhileSubscribed com.example.habits9.ui.settings  colors com.example.habits9.ui.settings  fillMaxSize com.example.habits9.ui.settings  fillMaxWidth com.example.habits9.ui.settings  height com.example.habits9.ui.settings  launch com.example.habits9.ui.settings  padding com.example.habits9.ui.settings  provideDelegate com.example.habits9.ui.settings  
selectable com.example.habits9.ui.settings  selectableGroup com.example.habits9.ui.settings  stateIn com.example.habits9.ui.settings  topAppBarColors com.example.habits9.ui.settings  userPreferencesRepository com.example.habits9.ui.settings  width com.example.habits9.ui.settings  SharingStarted 1com.example.habits9.ui.settings.SettingsViewModel  WhileSubscribed 1com.example.habits9.ui.settings.SettingsViewModel  firstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  launch 1com.example.habits9.ui.settings.SettingsViewModel  stateIn 1com.example.habits9.ui.settings.SettingsViewModel  updateFirstDayOfWeek 1com.example.habits9.ui.settings.SettingsViewModel  userPreferencesRepository 1com.example.habits9.ui.settings.SettingsViewModel  viewModelScope 1com.example.habits9.ui.settings.SettingsViewModel  Boolean com.example.habits9.utils  
ChronoUnit com.example.habits9.utils  	DayOfWeek com.example.habits9.utils  EnhancedFrequency com.example.habits9.utils  
FrequencyType com.example.habits9.utils  Habit com.example.habits9.utils  HabitScheduler com.example.habits9.utils  IllegalArgumentException com.example.habits9.utils  Int com.example.habits9.utils  	LocalDate com.example.habits9.utils  Long com.example.habits9.utils  TemporalAdjusters com.example.habits9.utils  fromDatabaseValues com.example.habits9.utils  java com.example.habits9.utils  toList com.example.habits9.utils  
ChronoUnit (com.example.habits9.utils.HabitScheduler  	DayOfWeek (com.example.habits9.utils.HabitScheduler  EnhancedFrequency (com.example.habits9.utils.HabitScheduler  
FrequencyType (com.example.habits9.utils.HabitScheduler  IllegalArgumentException (com.example.habits9.utils.HabitScheduler  	LocalDate (com.example.habits9.utils.HabitScheduler  TemporalAdjusters (com.example.habits9.utils.HabitScheduler  fromDatabaseValues (com.example.habits9.utils.HabitScheduler  isDailyHabitScheduled (com.example.habits9.utils.HabitScheduler  isHabitScheduled (com.example.habits9.utils.HabitScheduler  isMonthlyHabitScheduled (com.example.habits9.utils.HabitScheduler  isScheduledWithFrequency (com.example.habits9.utils.HabitScheduler  isSpecificWeekdayInMonth (com.example.habits9.utils.HabitScheduler  isWeeklyHabitScheduled (com.example.habits9.utils.HabitScheduler  java (com.example.habits9.utils.HabitScheduler  toList (com.example.habits9.utils.HabitScheduler  AndroidEntryPoint com.example.uhabits_99  Build com.example.uhabits_99  Bundle com.example.uhabits_99  ComponentActivity com.example.uhabits_99  CreateMeasurableHabitScreen com.example.uhabits_99  CreateYesNoHabitScreen com.example.uhabits_99  HabitDetailsScreen com.example.uhabits_99  HabitTypeSelectionScreen com.example.uhabits_99  
HomeScreen com.example.uhabits_99  MainActivity com.example.uhabits_99  ManageSectionsScreen com.example.uhabits_99  NavHost com.example.uhabits_99  RequiresApi com.example.uhabits_99  SettingsScreen com.example.uhabits_99  UHabits_99Theme com.example.uhabits_99  rememberNavController com.example.uhabits_99  Build #com.example.uhabits_99.MainActivity  CreateMeasurableHabitScreen #com.example.uhabits_99.MainActivity  CreateYesNoHabitScreen #com.example.uhabits_99.MainActivity  HabitDetailsScreen #com.example.uhabits_99.MainActivity  HabitTypeSelectionScreen #com.example.uhabits_99.MainActivity  
HomeScreen #com.example.uhabits_99.MainActivity  ManageSectionsScreen #com.example.uhabits_99.MainActivity  NavHost #com.example.uhabits_99.MainActivity  SettingsScreen #com.example.uhabits_99.MainActivity  UHabits_99Theme #com.example.uhabits_99.MainActivity  
composable #com.example.uhabits_99.MainActivity  rememberNavController #com.example.uhabits_99.MainActivity  
setContent #com.example.uhabits_99.MainActivity  Boolean com.example.uhabits_99.ui.theme  Build com.example.uhabits_99.ui.theme  
Composable com.example.uhabits_99.ui.theme  DarkColorScheme com.example.uhabits_99.ui.theme  
FontFamily com.example.uhabits_99.ui.theme  
FontWeight com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  UHabits_99Theme com.example.uhabits_99.ui.theme  Unit com.example.uhabits_99.ui.theme  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	DayOfWeek 	java.time  Instant 	java.time  	LocalDate 	java.time  	LocalTime 	java.time  ZoneId 	java.time  FRIDAY java.time.DayOfWeek  MONDAY java.time.DayOfWeek  SATURDAY java.time.DayOfWeek  SUNDAY java.time.DayOfWeek  THURSDAY java.time.DayOfWeek  TUESDAY java.time.DayOfWeek  	WEDNESDAY java.time.DayOfWeek  name java.time.DayOfWeek  to java.time.DayOfWeek  value java.time.DayOfWeek  atZone java.time.Instant  ofEpochMilli java.time.Instant  toEpochMilli java.time.Instant  atStartOfDay java.time.LocalDate  
dayOfMonth java.time.LocalDate  	dayOfWeek java.time.LocalDate  format java.time.LocalDate  get java.time.LocalDate  isAfter java.time.LocalDate  
lengthOfMonth java.time.LocalDate  	minusDays java.time.LocalDate  month java.time.LocalDate  now java.time.LocalDate  of java.time.LocalDate  
ofEpochDay java.time.LocalDate  plusDays java.time.LocalDate  with java.time.LocalDate  withDayOfMonth java.time.LocalDate  hour java.time.LocalTime  minute java.time.LocalTime  of java.time.LocalTime  
systemDefault java.time.ZoneId  	toInstant java.time.ZonedDateTime  toLocalDate java.time.ZonedDateTime  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  
ChronoUnit java.time.temporal  TemporalAdjusters java.time.temporal  
WeekFields java.time.temporal  DAYS java.time.temporal.ChronoUnit  MONTHS java.time.temporal.ChronoUnit  WEEKS java.time.temporal.ChronoUnit  between java.time.temporal.ChronoUnit  
nextOrSame $java.time.temporal.TemporalAdjusters  previousOrSame $java.time.temporal.TemporalAdjusters  of java.time.temporal.WeekFields  
weekOfYear java.time.temporal.WeekFields  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Inject javax.inject  	Singleton javax.inject  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  Enum kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  map kotlin  minus kotlin  plus kotlin  takeIf kotlin  to kotlin  toList kotlin  toString 
kotlin.Any  contains kotlin.Array  find kotlin.Array  forEach kotlin.Array  toList kotlin.Array  toSet kotlin.Array  not kotlin.Boolean  	uppercase kotlin.Char  	compareTo 
kotlin.Double  sp 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  toString 
kotlin.Double  AT_LEAST kotlin.Enum  AT_MOST kotlin.Enum  	Companion kotlin.Enum  DAILY kotlin.Enum  	DayOfWeek kotlin.Enum  
FrequencyType kotlin.Enum  	HabitType kotlin.Enum  IllegalStateException kotlin.Enum  Int kotlin.Enum  List kotlin.Enum  MONTHLY kotlin.Enum  	NUMERICAL kotlin.Enum  NumericalHabitType kotlin.Enum  String kotlin.Enum  WEEKLY kotlin.Enum  YES_NO kotlin.Enum  	emptyList kotlin.Enum  find kotlin.Enum  
isNullOrBlank kotlin.Enum  joinToString kotlin.Enum  map kotlin.Enum  
mapNotNull kotlin.Enum  split kotlin.Enum  toIntOrNull kotlin.Enum  values kotlin.Enum  AT_LEAST kotlin.Enum.Companion  AT_MOST kotlin.Enum.Companion  DAILY kotlin.Enum.Companion  IllegalStateException kotlin.Enum.Companion  MONTHLY kotlin.Enum.Companion  	NUMERICAL kotlin.Enum.Companion  WEEKLY kotlin.Enum.Companion  YES_NO kotlin.Enum.Companion  	emptyList kotlin.Enum.Companion  find kotlin.Enum.Companion  
isNullOrBlank kotlin.Enum.Companion  joinToString kotlin.Enum.Companion  map kotlin.Enum.Companion  
mapNotNull kotlin.Enum.Companion  split kotlin.Enum.Companion  toIntOrNull kotlin.Enum.Companion  values kotlin.Enum.Companion  message kotlin.Exception  printStackTrace kotlin.Exception  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  coerceAtMost 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  takeIf 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  div kotlin.Long  rem kotlin.Long  times kotlin.Long  to kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  format 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  replace 
kotlin.String  replaceFirstChar 
kotlin.String  split 
kotlin.String  take 
kotlin.String  toDoubleOrNull 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
Collection kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  	associate kotlin.collections  
associateWith kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  find kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  groupBy kotlin.collections  indexOfFirst kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  mapKeys kotlin.collections  
mapNotNull kotlin.collections  	mapValues kotlin.collections  minus kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  setOf kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  toList kotlin.collections  
toMutableList kotlin.collections  toMutableMap kotlin.collections  toSet kotlin.collections  any kotlin.collections.Collection  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  all kotlin.collections.List  any kotlin.collections.List  	associate kotlin.collections.List  
associateWith kotlin.collections.List  average kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  groupBy kotlin.collections.List  indexOfFirst kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  
mapNotNull kotlin.collections.List  size kotlin.collections.List  
toMutableList kotlin.collections.List  toSet kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  mapKeys kotlin.collections.Map  	mapValues kotlin.collections.Map  toMutableMap kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  
mapIndexed kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  contains kotlin.collections.Set  first kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  minus kotlin.collections.Set  plus kotlin.collections.Set  size kotlin.collections.Set  sortedBy kotlin.collections.Set  toList kotlin.collections.Set  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  SuspendFunction5 kotlin.coroutines  java 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  coerceAtMost 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  contains kotlin.ranges.IntRange  map kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  	associate kotlin.sequences  
associateWith kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  groupBy kotlin.sequences  indexOfFirst kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  
mapNotNull kotlin.sequences  minus kotlin.sequences  plus kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  toSet kotlin.sequences  all kotlin.text  any kotlin.text  	associate kotlin.text  
associateWith kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  find kotlin.text  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  groupBy kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapIndexed kotlin.text  
mapNotNull kotlin.text  plus kotlin.text  replace kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  set kotlin.text  split kotlin.text  take kotlin.text  toDoubleOrNull kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  
toMutableList kotlin.text  toSet kotlin.text  trim kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  
Completion !kotlinx.coroutines.CoroutineScope  HabitSection !kotlinx.coroutines.CoroutineScope  	HabitType !kotlinx.coroutines.CoroutineScope  _completionValuesState !kotlinx.coroutines.CoroutineScope  _completionsState !kotlinx.coroutines.CoroutineScope  _dialogState !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  	associate !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  completionRepository !kotlinx.coroutines.CoroutineScope  completionsFlow !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  groupBy !kotlinx.coroutines.CoroutineScope  habitRepository !kotlinx.coroutines.CoroutineScope  habitSectionRepository !kotlinx.coroutines.CoroutineScope  hideCreateDialog !kotlinx.coroutines.CoroutineScope  hideDeleteDialog !kotlinx.coroutines.CoroutineScope  hideEditDialog !kotlinx.coroutines.CoroutineScope  hideMeasurableHabitDialog !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	mapValues !kotlinx.coroutines.CoroutineScope  mutableMapOf !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  toDoubleOrNull !kotlinx.coroutines.CoroutineScope  toMutableMap !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  userPreferencesRepository !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  
flatMapLatest kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              