package com.example.habits9.di;

import com.example.habits9.data.HabitDao;
import com.example.habits9.data.HabitDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitDaoFactory implements Factory<HabitDao> {
  private final Provider<HabitDatabase> habitDatabaseProvider;

  public DatabaseModule_ProvideHabitDaoFactory(Provider<HabitDatabase> habitDatabaseProvider) {
    this.habitDatabaseProvider = habitDatabaseProvider;
  }

  @Override
  public HabitDao get() {
    return provideHabitDao(habitDatabaseProvider.get());
  }

  public static DatabaseModule_ProvideHabitDaoFactory create(
      Provider<HabitDatabase> habitDatabaseProvider) {
    return new DatabaseModule_ProvideHabitDaoFactory(habitDatabaseProvider);
  }

  public static HabitDao provideHabitDao(HabitDatabase habitDatabase) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitDao(habitDatabase));
  }
}
