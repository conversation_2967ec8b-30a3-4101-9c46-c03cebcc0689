package com.example.habits9.di;

import com.example.habits9.data.HabitSectionDao;
import com.example.habits9.data.HabitSectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideHabitSectionRepositoryFactory implements Factory<HabitSectionRepository> {
  private final Provider<HabitSectionDao> habitSectionDaoProvider;

  public DatabaseModule_ProvideHabitSectionRepositoryFactory(
      Provider<HabitSectionDao> habitSectionDaoProvider) {
    this.habitSectionDaoProvider = habitSectionDaoProvider;
  }

  @Override
  public HabitSectionRepository get() {
    return provideHabitSectionRepository(habitSectionDaoProvider.get());
  }

  public static DatabaseModule_ProvideHabitSectionRepositoryFactory create(
      Provider<HabitSectionDao> habitSectionDaoProvider) {
    return new DatabaseModule_ProvideHabitSectionRepositoryFactory(habitSectionDaoProvider);
  }

  public static HabitSectionRepository provideHabitSectionRepository(
      HabitSectionDao habitSectionDao) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideHabitSectionRepository(habitSectionDao));
  }
}
